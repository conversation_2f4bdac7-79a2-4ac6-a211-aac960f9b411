diff --git a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
index 12f42277c..b76c8f100 100644
--- a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
+++ b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
@@ -55,6 +55,9 @@ class ShippingRateListDataProvider extends \Magento\Ui\DataProvider\AbstractData
         $this->collection->addFieldToFilter(
             'seller_id',
             ['eq' => $this->helper->getSellerId()]
+        )->addFieldToFilter(
+            'min_order_amount',
+            ['eq' => 0]
         );
     }
 }
diff --git a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ThresholdListDataProvider.php b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ThresholdListDataProvider.php
new file mode 100644
index *********..2a17e7e0f
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ThresholdListDataProvider.php
@@ -0,0 +1,97 @@
+<?php
+/**
+ * Data provider for Free Shipping Threshold listing
+ */
+declare(strict_types=1);
+
+namespace Coditron\CustomShippingRate\Ui\DataProvider;
+
+use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
+use Magento\Framework\Api\Filter;
+use Magento\Ui\DataProvider\AbstractDataProvider;
+use Webkul\Marketplace\Helper\Data as MarketplaceHelper;
+
+class ThresholdListDataProvider extends AbstractDataProvider
+{
+    /**
+     * @var MarketplaceHelper
+     */
+    protected $marketplaceHelper;
+
+    /**
+     * @var array
+     */
+    protected $loadedData;
+
+    /**
+     * @param string $name
+     * @param string $primaryFieldName
+     * @param string $requestFieldName
+     * @param CollectionFactory $collectionFactory
+     * @param MarketplaceHelper $marketplaceHelper
+     * @param array $meta
+     * @param array $data
+     */
+    public function __construct(
+        $name,
+        $primaryFieldName,
+        $requestFieldName,
+        CollectionFactory $collectionFactory,
+        MarketplaceHelper $marketplaceHelper,
+        array $meta = [],
+        array $data = []
+    ) {
+        $this->collection = $collectionFactory->create();
+        $this->marketplaceHelper = $marketplaceHelper;
+        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
+    }
+
+    /**
+     * Get data
+     *
+     * @return array
+     */
+    public function getData()
+    {
+        if (isset($this->loadedData)) {
+            return $this->loadedData;
+        }
+
+        $sellerId = $this->marketplaceHelper->getCustomerId();
+
+        // Filter collection to show only records with min_order_amount > 0 for current seller
+        $this->collection->addFieldToFilter('seller_id', $sellerId)
+                        ->addFieldToFilter('min_order_amount', ['gt' => 0]);
+
+        $this->loadedData = $this->collection->toArray();
+        return $this->loadedData;
+    }
+
+    /**
+     * Add filter
+     *
+     * @param Filter $filter
+     * @return void
+     */
+    public function addFilter(Filter $filter)
+    {
+        if ($filter->getField() !== 'fulltext') {
+            $this->collection->addFieldToFilter(
+                $filter->getField(),
+                [$filter->getConditionType() => $filter->getValue()]
+            );
+        } else {
+            $value = trim($filter->getValue());
+            $this->collection->addFieldToFilter(
+                [
+                    ['attribute' => 'countries'],
+                    ['attribute' => 'min_order_amount']
+                ],
+                [
+                    ['like' => "%{$value}%"],
+                    ['like' => "%{$value}%"]
+                ]
+            );
+        }
+    }
+}
diff --git a/app/code/Coditron/CustomShippingRate/etc/db_schema.xml b/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
index 68559032e..f1710aec1 100644
--- a/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
+++ b/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
@@ -21,6 +21,7 @@
         <column name="weight" nullable="true" xsi:type="varchar" comment="Weight (Up to, in Kg)" length="255"/>
         <column name="shipping_price" nullable="true" xsi:type="varchar" comment="Shipping price" length="255"/>
         <column name="free_shipping" nullable="false" xsi:type="boolean" default="false" comment="Free Shipping"/>
+        <column name="min_order_amount" nullable="true" xsi:type="decimal" precision="12" scale="4" default="0.0000" comment="Minimum Order Amount for Free Shipping"/>
 		<column name="seller_id" xsi:type="int" unsigned="true" nullable="false" identity="false" padding="10" default="0" comment="Seller Id"/>
 		<constraint xsi:type="foreign" referenceId="FK_CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" table="coditron_customshippingrate_shiptablerates" column="seller_id" referenceTable="customer_entity" referenceColumn="entity_id" onDelete="CASCADE"/>
         <index referenceId="CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" indexType="btree">
diff --git a/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json b/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
index 3d5012378..c9b75af9f 100644
--- a/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
+++ b/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
@@ -22,6 +22,7 @@
             "weight": true,
             "shipping_price": true,
             "free_shipping": true,
+            "min_order_amount": true,
             "seller_id": true
         },
         "index": {
diff --git a/app/code/Coditron/CustomShippingRate/etc/di.xml b/app/code/Coditron/CustomShippingRate/etc/di.xml
index 68c6a903c..96187ef7f 100644
--- a/app/code/Coditron/CustomShippingRate/etc/di.xml
+++ b/app/code/Coditron/CustomShippingRate/etc/di.xml
@@ -6,6 +6,9 @@
 	<type name="Magento\Checkout\Model\ShippingInformationManagement">
 		<plugin name="update_shipping_data_on_method_selection" type="Coditron\CustomShippingRate\Plugin\UpdateShippingDataOnMethodSelection" />
 	</type>
+	<type name="Magento\OfflineShipping\Model\Carrier\Freeshipping">
+		<plugin name="disable_core_freeshipping_when_thresholds_exist" type="Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier\FreeShippingPlugin" />
+	</type>
 	<preference for="Coditron\CustomShippingRate\Api\CustomShippingInformationManagementInterface" type="Coditron\CustomShippingRate\Model\CustomShippingInformationManagement" />
 	<preference for="Coditron\CustomShippingRate\Api\Data\CustomShippingInformationInterface"
                 type="Coditron\CustomShippingRate\Model\Data\CustomShippingInformation" />
diff --git a/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml b/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml
index 63e68d639..43c4b463d 100755
--- a/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml
@@ -40,8 +40,40 @@ $taxHelper = $block->getData('taxHelper');
                                 id="s_method_<?= $block->escapeHtmlAttr($_code) ?>" <?= /* @noEscape */ $_checked ?>
                                                                  class="admin__control-radio required-entry"/>
                             <label class="admin__field-label" for="s_method_<?= $block->escapeHtmlAttr($_code) ?>">
-                                <?= $block->escapeHtml($_rate->getMethodTitle() ?
-                                    $_rate->getMethodTitle() : $_rate->getMethodDescription()) ?> -
+                                <?php
+                                $listMethodTitle = $_rate->getMethodTitle() ? $_rate->getMethodTitle() : $_rate->getMethodDescription();
+
+                                if ($_rate->getCarrier() === 'freeshipping' && strpos($listMethodTitle, '(For orders over') === false) {
+                                    $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
+                                    $shipTableRatesCollectionFactory = $objectManager->get(\Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory::class);
+
+                                    $quote = $block->getQuote();
+                                    $country = $quote->getShippingAddress()->getCountryId();
+                                    $subtotal = $quote->getBaseSubtotalWithDiscount();
+
+                                    if ($country && $subtotal > 0) {
+                                        $collection = $shipTableRatesCollectionFactory->create();
+                                        $collection->addFieldToFilter('free_shipping', 1)
+                                                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
+                                                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal]);
+
+                                        $lowestThreshold = null;
+                                        foreach ($collection as $threshold) {
+                                            $countries = $threshold->getCountries();
+                                            if (in_array($country, $countries)) {
+                                                if ($lowestThreshold === null || $threshold->getMinOrderAmount() < $lowestThreshold) {
+                                                    $lowestThreshold = $threshold->getMinOrderAmount();
+                                                }
+                                            }
+                                        }
+
+                                        if ($lowestThreshold !== null) {
+                                            $listMethodTitle .= sprintf(' (For orders over $%.2f)', $lowestThreshold);
+                                        }
+                                    }
+                                }
+                                ?>
+                                <?= $block->escapeHtml($listMethodTitle) ?> -
                                 <strong>
                                     <?php $_excl = $block->getShippingPrice(
                                         $_rate->getPrice(),
@@ -111,7 +143,41 @@ $taxHelper = $block->getData('taxHelper');
                     <?= $block->escapeHtml($block->getCarrierName($_rate->getCarrier())) ?>
                 </dt>
                 <dd class="admin__order-shipment-methods-options">
-                    <?= $block->escapeHtml($_rate->getMethodTitle() ? $_rate->getMethodTitle() : $_rate->getMethodDescription()) ?> -
+                    <?php
+                    $methodTitle = $_rate->getMethodTitle() ? $_rate->getMethodTitle() : $_rate->getMethodDescription();
+
+                    // Add threshold subtitle for free shipping if not already present
+                    if ($_rate->getCarrier() === 'freeshipping' && strpos($methodTitle, '(For orders over') === false) {
+                        $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
+                        $shipTableRatesCollectionFactory = $objectManager->get(\Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory::class);
+
+                        $quote = $block->getQuote();
+                        $country = $quote->getShippingAddress()->getCountryId();
+                        $subtotal = $quote->getBaseSubtotalWithDiscount();
+
+                        if ($country && $subtotal > 0) {
+                            $collection = $shipTableRatesCollectionFactory->create();
+                            $collection->addFieldToFilter('free_shipping', 1)
+                                       ->addFieldToFilter('min_order_amount', ['gt' => 0])
+                                       ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal]);
+
+                            $lowestThreshold = null;
+                            foreach ($collection as $threshold) {
+                                $countries = $threshold->getCountries();
+                                if (in_array($country, $countries)) {
+                                    if ($lowestThreshold === null || $threshold->getMinOrderAmount() < $lowestThreshold) {
+                                        $lowestThreshold = $threshold->getMinOrderAmount();
+                                    }
+                                }
+                            }
+
+                            if ($lowestThreshold !== null) {
+                                $methodTitle .= sprintf(' (For orders over $%.2f)', $lowestThreshold);
+                            }
+                        }
+                    }
+                    ?>
+                    <?= $block->escapeHtml($methodTitle) ?> -
                     <strong>
                         <?php $_excl = $block->getShippingPrice(
                             $_rate->getPrice(),
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/layout/mpsellership_layout2_threshold_edit.xml b/app/code/Coditron/CustomShippingRate/view/frontend/layout/mpsellership_layout2_threshold_edit.xml
new file mode 100644
index *********..761c13ee4
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/layout/mpsellership_layout2_threshold_edit.xml
@@ -0,0 +1,17 @@
+<?xml version="1.0"?>
+
+<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
+    <head>
+
+        <css src="Webkul_Marketplace::css/wk_block.css"/>
+        <css src="Webkul_Marketplace::css/style.css"/>
+        <css src="Webkul_Marketplace::css/product.css"/>
+        <css src="Webkul_Marketplace::css/layout.css"/>
+        <css src="Coditron_CustomShippingRate::css/select2/select2.css"/>
+    </head>
+    <body>
+        <referenceContainer name="seller.content">
+            <block class="Coditron\CustomShippingRate\Block\TableRates" name="mpsellership_threshold_edit" template="Coditron_CustomShippingRate::shiprate/edit_threshold.phtml" cacheable="false"></block>
+        </referenceContainer>
+    </body>
+</page>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml b/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
index 972c401ce..73e7e885b 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
@@ -20,6 +20,7 @@
         </referenceContainer>
         <referenceContainer name="sellership_rate_manage">
             <uiComponent name="sellership_rates_list_front"/>
+            <uiComponent name="sellership_threshold_list_front"/>
         </referenceContainer>
     </body>
 </page>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
index 3894ab393..a6b06a5e1 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
@@ -1,7 +1,7 @@
 <?php
      $marketplaceHelper = $block->getMpHelper();
      $isPartner= $marketplaceHelper->isSeller();
-     $isEnable = false;
+     $isEnable = $block->isenable();
      $magentoCurrentUrl = $block->getCurrentUrl();
 ?>
 <?php if ($isPartner): ?>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/edit_threshold.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/edit_threshold.phtml
new file mode 100644
index *********..b26904af9
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/edit_threshold.phtml
@@ -0,0 +1,114 @@
+<?php
+/** @var \Coditron\CustomShippingRate\Block\TableRates $block */
+$helper = $block->getMpHelper();
+$isPartner = $helper->isSeller();
+$backUrl = $block->getStoreUrl().'coditron_customshippingrate/shiptablerates/manage/';
+
+$sellerId = $block->getSellerId();
+$shipRate = $block->getShipRate();
+
+$countriesListHtml = $block->getCountries(true, $shipRate->getCountries());
+?>
+<div class="wk-mpsellercategory-container">
+    <?php if ($isPartner == 1): ?>
+        <form action="<?= $escaper->escapeUrl($block->getUrl('coditron_customshippingrate/shiptablerates/save')) ?>"
+        enctype="multipart/form-data" method="post" id="form-save-threshold"
+        data-mage-init='{"validation":{}}'>
+            <div class="fieldset wk-ui-component-container">
+                <?= $block->getBlockHtml('formkey') ?>
+                <?= $block->getBlockHtml('seller.formkey') ?>
+                <input type="hidden" name="id" value="<?= $escaper->escapeHtml($shipRate->getShiptableratesId()) ?>">
+                <input type="hidden" name="seller_id" value="<?= $escaper->escapeHtml($sellerId) ?>">
+                <input type="hidden" name="is_threshold" value="1">
+                
+                <!-- Set default values for threshold mode -->
+                <input type="hidden" name="courier_name" value="Free Shipping">
+                <input type="hidden" name="service_type" value="free_shipping">
+                <input type="hidden" name="weight" value="999999">
+                <input type="hidden" name="shipping_price" value="0">
+                <input type="hidden" name="free_shipping" value="1">
+                <input type="hidden" name="packing_time" value="0">
+                <input type="hidden" name="delivery_time" value="0">
+                <input type="hidden" name="total_lead_time" value="0">
+                <input type="hidden" name="return_address_id" value="0">
+                
+                <div class="page-main-actions">
+                    <div class="page-actions-placeholder"></div>
+                    <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                        <div class="page-actions-inner" data-title="<?= $escaper->escapeHtml(__("Free Shipping Threshold")); ?>">
+                            <div class="page-actions-buttons">
+                                <button id="back" title="<?= $escaper->escapeHtml(__("Back")); ?>" type="button"
+                                class="action- scalable back wk-ui-grid-btn-back wk-ui-grid-btn"
+                                data-ui-id="back-button">
+                                    <span><?= $escaper->escapeHtml(__("Back")); ?></span>
+                                </button>
+                                <button id="save"
+                                title="<?= $escaper->escapeHtml(__("Save Free Shipping Threshold")); ?>" type="submit"
+                                class="action- scalable save primary ui-button ui-widget
+                                ui-state-default ui-corner-all ui-button-text-only wk-ui-grid-btn
+                                wk-ui-grid-btn-primary"
+                                data-form-role="save"
+                                data-ui-id="save-button" role="button" aria-disabled="false">
+                                    <span class="ui-button-text">
+                                        <span><?= $escaper->escapeHtml(__("Save Free Shipping Threshold")); ?></span>
+                                    </span>
+                                </button>
+                            </div>
+                        </div>
+                    </div>
+                </div>
+                
+                <div class="field required">
+                    <label class="label" for="countries">
+                        <span><?php /* @escapeNotVerified */ echo __('Countries') ?></span>
+                    </label>
+                    <div class="tooltip">
+                    <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
+                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select countries where free shipping applies')) ?></span>
+                    </div>
+                    <div class="control">
+                        <?php echo $countriesListHtml; ?>
+                    </div>
+                </div>
+                
+                <div class="field required">
+                    <label for="min_order_amount" class="label">
+                        <span><?= $escaper->escapeHtml(__("Minimum Order Amount (USD)")); ?></span>
+                    </label>
+                    <div class="tooltip">
+                     <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
+                     <span class="tooltiptext"><?= $escaper->escapeHtml(__('Minimum order amount required for free shipping')) ?></span>
+                     </div>
+                    <div class="control">
+                        <input type="text" class="input-text required-entry validate-number validate-zero-or-greater" 
+                        name="min_order_amount"
+                        data-validate="{required:true, 'validate-number':true, 'validate-zero-or-greater':true}" 
+                        title="<?= $escaper->escapeHtml(__("Minimum Order Amount")); ?>"
+                        id="min_order_amount" 
+                        value="<?= $escaper->escapeHtml($block->escapeHtml($shipRate->getMinOrderAmount())) ?>">
+                    </div>
+                </div>
+            </div>
+        </form>
+    <?php else: ?>
+        <h2 class="wk-mp-error-msg">
+            <?= $escaper->escapeHtml(__("To Become Seller Please Contact to Admin.")); ?>
+        </h2>
+    <?php endif; ?>
+</div>
+
+<script type="text/x-magento-init">
+{
+    "#form-save-threshold": {
+        "Coditron_CustomShippingRate/js/threshold-form": {
+            "backUrl": "<?= $escaper->escapeJs($backUrl) ?>"
+        }
+    }
+}
+</script>
+
+<style>
+    .select2-search__field {
+        height: auto !important;
+    }
+</style>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
index 15601be5f..351fae526 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
@@ -10,21 +10,51 @@
  */
 ?>
 <div class="wk-mpsellercategory-container">
-    <div class="page-main-actions">
-        <div class="page-actions-placeholder"></div>
-        <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
-            <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
-                <div class="page-actions-buttons">
-                    <button id="add" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
-                    class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
-                    onclick="location.href
-                    = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
-                    data-ui-id="add-button">
-                        <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
-                    </button>
+    <!-- Shipping Methods Section -->
+    <div class="wk-mp-section">
+        <h3 class="wk-mp-section-title"><?= $escaper->escapeHtml(__('Shipping Methods')) ?></h3>
+        <div class="page-main-actions">
+            <div class="page-actions-placeholder"></div>
+            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
+                    <div class="page-actions-buttons">
+                        <button id="add-shipping-method" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
+                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
+                        onclick="location.href
+                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
+                        data-ui-id="add-button">
+                            <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
+                        </button>
+                    </div>
                 </div>
             </div>
         </div>
+        <div id="shipping-methods-grid">
+            <?= /* @noEscape */ $block->getChildHtml('sellership_rates_list_front'); ?>
+        </div>
+    </div>
+
+    <!-- Free Shipping Thresholds Section -->
+    <div class="wk-mp-section" style="margin-top: 40px;">
+        <h3 class="wk-mp-section-title"><?= $escaper->escapeHtml(__('Free Shipping Thresholds')) ?></h3>
+        <div class="page-main-actions">
+            <div class="page-actions-placeholder"></div>
+            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Free Shipping Threshold")); ?>'>
+                    <div class="page-actions-buttons">
+                        <button id="add-threshold" title='<?= $escaper->escapeHtml(__("Add New Threshold")); ?>' type="button"
+                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
+                        onclick="location.href
+                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new', ['is_threshold' => 1]))?>';"
+                        data-ui-id="add-threshold-button">
+                            <span><?= $escaper->escapeHtml(__("Add New Threshold")); ?></span>
+                        </button>
+                    </div>
+                </div>
+            </div>
+        </div>
+        <div id="threshold-grid">
+            <?= /* @noEscape */ $block->getChildHtml('sellership_threshold_list_front'); ?>
+        </div>
     </div>
-    <?= /* @noEscape */ $block->getChildHtml(); ?>
 </div>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/ui_component/sellership_threshold_list_front.xml b/app/code/Coditron/CustomShippingRate/view/frontend/ui_component/sellership_threshold_list_front.xml
new file mode 100644
index *********..f52c8a21b
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/ui_component/sellership_threshold_list_front.xml
@@ -0,0 +1,119 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<!--
+/**
+ * Free Shipping Threshold UI Component for Seller Dashboard
+ */
+-->
+<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
+    <argument name="data" xsi:type="array">
+        <item name="js_config" xsi:type="array">
+            <item name="provider" xsi:type="string">sellership_threshold_list_front.sellership_threshold_list_front_data_source</item>
+            <item name="deps" xsi:type="string">sellership_threshold_list_front.sellership_threshold_list_front_data_source</item>
+        </item>
+        <item name="spinner" xsi:type="string">sellership_threshold_columns</item>
+    </argument>
+    <dataSource name="sellership_threshold_list_front_data_source">
+        <argument name="dataProvider" xsi:type="configurableObject">
+            <argument name="class" xsi:type="string">Coditron\CustomShippingRate\Ui\DataProvider\ThresholdListDataProvider</argument>
+            <argument name="name" xsi:type="string">sellership_threshold_list_front_data_source</argument>
+            <argument name="primaryFieldName" xsi:type="string">shiptablerates_id</argument>
+            <argument name="requestFieldName" xsi:type="string">id</argument>
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/provider</item>
+                    <item name="update_url" xsi:type="url" path="marketplace/mui_index/render"/>
+                    <item name="storageConfig" xsi:type="array">
+                        <item name="cacheRequests" xsi:type="boolean">false</item>
+                    </item>
+                </item>
+            </argument>
+        </argument>
+    </dataSource>
+    <listingToolbar name="listing_top">
+        <columnsControls name="columns_controls"/>
+        <filters name="listing_filters">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="statefull" xsi:type="array">
+                        <item name="applied" xsi:type="boolean">false</item>
+                    </item>
+                    <item name="params" xsi:type="array">
+                        <item name="filters_modifier" xsi:type="array" />
+                    </item>
+                </item>
+            </argument>
+        </filters>
+        <massaction name="listing_massaction">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="selectProvider" xsi:type="string">sellership_threshold_list_front.sellership_threshold_list_front.sellership_threshold_columns.ids</item>
+                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
+                </item>
+            </argument>
+            <action name="delete">
+                <argument name="data" xsi:type="array">
+                    <item name="config" xsi:type="array">
+                        <item name="type" xsi:type="string">delete</item>
+                        <item name="label" xsi:type="string" translate="true">Delete</item>
+                        <item name="url" xsi:type="url" path="coditron_customshippingrate/shiptablerates/delete"/>
+                        <item name="confirm" xsi:type="array">
+                            <item name="title" xsi:type="string" translate="true">Delete Threshold</item>
+                            <item name="message" xsi:type="string" translate="true">Are you sure you want to delete selected free shipping thresholds?</item>
+                        </item>
+                    </item>
+                </argument>
+            </action>
+        </massaction>
+        <paging name="listing_paging"/>
+    </listingToolbar>
+    <columns name="sellership_threshold_columns">
+        <argument name="data" xsi:type="array">
+            <item name="config" xsi:type="array">
+                <item name="childDefaults" xsi:type="array">
+                    <item name="fieldAction" xsi:type="array">
+                        <item name="provider" xsi:type="string">thresholdGrid</item>
+                        <item name="target" xsi:type="string">selectThreshold</item>
+                        <item name="params" xsi:type="array">
+                            <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
+                        </item>
+                    </item>
+                </item>
+            </item>
+        </argument>
+        <selectionsColumn name="ids">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
+                </item>
+            </argument>
+        </selectionsColumn>
+        <column name="countries">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="filter" xsi:type="string">text</item>
+                    <item name="label" xsi:type="string" translate="true">Countries</item>
+                    <item name="sortOrder" xsi:type="number">20</item>
+                </item>
+            </argument>
+        </column>
+        <column name="min_order_amount">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="filter" xsi:type="string">text</item>
+                    <item name="label" xsi:type="string" translate="true">Minimum Order Amount (USD)</item>
+                    <item name="sortOrder" xsi:type="number">30</item>
+                </item>
+            </argument>
+        </column>
+        <actionsColumn name="actions" class="Coditron\CustomShippingRate\Ui\Component\Listing\Column\ThresholdActions">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
+                    <item name="viewUrlPath" xsi:type="string">coditron_customshippingrate/shiptablerates/editthreshold</item>
+                    <item name="urlEntityParamName" xsi:type="string">id</item>
+                    <item name="sortOrder" xsi:type="number">100</item>
+                </item>
+            </argument>
+        </actionsColumn>
+    </columns>
+</listing>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/web/js/threshold-form.js b/app/code/Coditron/CustomShippingRate/view/frontend/web/js/threshold-form.js
new file mode 100644
index *********..36ed42380
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/web/js/threshold-form.js
@@ -0,0 +1,16 @@
+define(['jquery', 'select2'], function($) {
+    'use strict';
+
+    return function(config, element) {
+        $(document).ready(function() {
+            $("#back").click(function(){
+                window.location.replace(config.backUrl);
+            });
+
+            $('.custom-multiselect').select2({
+                placeholder: "Select countries",
+                allowClear: true
+            });
+        });
+    };
+});
