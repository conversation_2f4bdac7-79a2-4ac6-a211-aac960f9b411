diff --git a/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php b/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
index 5529cd649..202b35f6f 100644
--- a/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
+++ b/app/code/Coditron/CustomShippingRate/Api/Data/ShipTableRatesInterface.php
@@ -18,6 +18,7 @@ interface ShipTableRatesInterface
     const WEIGHT = 'weight';
     const SHIPPING_PRICE = 'shipping_price';
     const FREE_SHIPPING = 'free_shipping';
+    const MIN_ORDER_AMOUNT = 'min_order_amount';
 
     /**
      * Get shiptablerates_id
@@ -175,5 +176,18 @@ interface ShipTableRatesInterface
      */
     public function setFreeShipping(bool $freeShipping): ShipTableRatesInterface;
 
+    /**
+     * Get minimum order amount
+     * @return float|null
+     */
+    public function getMinOrderAmount(): ?float;
+
+    /**
+     * Set minimum order amount
+     * @param float $minOrderAmount
+     * @return \Coditron\CustomShippingRate\Api\Data\ShipTableRatesInterface
+     */
+    public function setMinOrderAmount(float $minOrderAmount): ShipTableRatesInterface;
+
 }
 
diff --git a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Edit.php b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Edit.php
index 07c62a935..1a9b31309 100644
--- a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Edit.php
+++ b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Edit.php
@@ -5,7 +5,8 @@ namespace Coditron\CustomShippingRate\Controller\ShipTableRates;
 class Edit extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 {
     /**
-     * Seller Category Edit action.
+     * Seller Shipping Rate Edit action.
+     * Handles both regular shipping methods and free shipping thresholds.
      *
      * @return \Magento\Framework\Controller\Result\RedirectFactory
      */
@@ -18,24 +19,36 @@ class Edit extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
             );
         }
 
+        $isThreshold = $this->getRequest()->getParam('is_threshold', false);
+
+        $id = $this->getRequest()->getParam('id') ?: $this->getRequest()->getParam('shiptablerates_id');
+        if ($id && !$this->getRequest()->getParam('shiptablerates_id')) {
+            $this->getRequest()->setParam('shiptablerates_id', $id);
+        }
+
         $resultPage = $this->_resultPageFactory->create();
         if ($this->_marketplaceHelper->getIsSeparatePanel()) {
-            $resultPage->addHandle('mpsellership_layout2_rate_edit');
+            if ($isThreshold) {
+                $resultPage->addHandle('mpsellership_layout2_threshold_edit');
+            } else {
+                $resultPage->addHandle('mpsellership_layout2_rate_edit');
+            }
         }
 
         if (!empty($this->getRequest()->getParam("shiptablerates_id"))) {
             $sellerShiprate = $this->getSellerShiprate();
             if (empty($sellerShiprate->getShiptableratesId())) {
-                $this->messageManager->addError("Shipping method does not exist");
+                $errorMessage = $isThreshold ? "Free shipping threshold does not exist" : "Shipping method does not exist";
+                $this->messageManager->addError($errorMessage);
                 return $this->resultRedirectFactory->create()->setPath(
                     'coditron_customshippingrate/shiptablerates/manage',
                     ['_secure' => $this->getRequest()->isSecure()]
                 );
             }
 
-            $title = $sellerShiprate->getCourierName();
+            $title = $isThreshold ? "Edit Free Shipping Threshold" : $sellerShiprate->getCourierName();
         } else {
-            $title = "New Shipping Method";
+            $title = $isThreshold ? "New Free Shipping Threshold" : "New Shipping Method";
         }
 
         $resultPage->getConfig()->getTitle()->set(__($title));
diff --git a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php
index e3f223dd4..e9e6685ee 100644
--- a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php
+++ b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Manage.php
@@ -10,7 +10,7 @@
  */
 namespace Coditron\CustomShippingRate\Controller\ShipTableRates;
 
-class Manage extends \Webkul\MpSellerCategory\Controller\AbstractCategory
+class Manage extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 {
     /**
      * Execute Method
diff --git a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
index a848ecfba..ab4eaef14 100644
--- a/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
+++ b/app/code/Coditron/CustomShippingRate/Controller/ShipTableRates/Save.php
@@ -51,12 +51,23 @@ class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
 
                 $sellerShiprate->save();
                 $id = $sellerShiprate->getShiptableratesId();
-                $this->messageManager->addSuccess(__("Shipping Rate saved successfully."));
-                $this->_helper->clearCache();
-                return $this->resultRedirectFactory->create()->setPath(
-                    'coditron_customshippingrate/shiptablerates/edit',
-                    ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()]
-                );
+
+                $isThreshold = $this->getRequest()->getParam('is_threshold', false);
+                if ($isThreshold) {
+                    $this->messageManager->addSuccess(__("Free Shipping Threshold saved successfully."));
+                    $this->_helper->clearCache();
+                    return $this->resultRedirectFactory->create()->setPath(
+                        'coditron_customshippingrate/shiptablerates/edit',
+                        ['shiptablerates_id' => $id, 'is_threshold' => 1, '_secure' => $this->getRequest()->isSecure()]
+                    );
+                } else {
+                    $this->messageManager->addSuccess(__("Shipping Rate saved successfully."));
+                    $this->_helper->clearCache();
+                    return $this->resultRedirectFactory->create()->setPath(
+                        'coditron_customshippingrate/shiptablerates/edit',
+                        ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()]
+                    );
+                }
             } catch (\Exception $e) {
                 $this->messageManager->addError($e->getMessage());
                 return $this->resultRedirectFactory->create()->setPath(
diff --git a/app/code/Coditron/CustomShippingRate/Model/Carrier.php b/app/code/Coditron/CustomShippingRate/Model/Carrier.php
index 8d1ad90e1..a094e5ab4 100644
--- a/app/code/Coditron/CustomShippingRate/Model/Carrier.php
+++ b/app/code/Coditron/CustomShippingRate/Model/Carrier.php
@@ -158,6 +158,10 @@ class Carrier extends AbstractCarrier implements CarrierInterface
                     continue;
                 }
 
+                if ($tableRate->getMinAmount() && $tableRate->getMinAmount() > 0) {
+                    continue;
+                }
+
                 [, $sellerId] = explode('|', $sellerKey);
                 $totalPrice += $tableRate->getShippingPrice();
                 $minLeadTime = $minLeadTime === null ? $tableRate->getTotalLeadTime() : min($minLeadTime, $tableRate->getTotalLeadTime());
diff --git a/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php b/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
index 5369590c3..7c24e25d2 100644
--- a/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
+++ b/app/code/Coditron/CustomShippingRate/Model/ShipTableRates.php
@@ -215,6 +215,22 @@ class ShipTableRates extends AbstractModel implements ShipTableRatesInterface
         return $this->setData(self::FREE_SHIPPING, $freeShipping);
     }
 
+    /**
+     * @inheritDoc
+     */
+    public function getMinOrderAmount(): ?float
+    {
+        return (float)$this->getData(self::MIN_ORDER_AMOUNT);
+    }
+
+    /**
+     * @inheritDoc
+     */
+    public function setMinOrderAmount(float $minOrderAmount): ShipTableRatesInterface
+    {
+        return $this->setData(self::MIN_ORDER_AMOUNT, $minOrderAmount);
+    }
+
     /**
      * Override to Converts countries array to string
      * @param $key
diff --git a/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php b/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
index 4043e1b79..95b7f3aee 100755
--- a/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
+++ b/app/code/Coditron/CustomShippingRate/Plugin/Quote/Address/Total/ShippingPlugin.php
@@ -15,6 +15,7 @@ use Magento\Quote\Model\Quote\Address\Total;
 use Magento\Quote\Model\Quote\Address\Total\Shipping;
 use Coditron\CustomShippingRate\Helper\Data;
 use Coditron\CustomShippingRate\Model\Carrier;
+use Coditron\CustomShippingRate\Api\ShipTableRatesRepositoryInterface;
 
 class ShippingPlugin
 {
@@ -23,13 +24,21 @@ class ShippingPlugin
      */
     protected $customShippingRateHelper;
 
+    /**
+     * @var ShipTableRatesRepositoryInterface
+     */
+    protected $shipTableRatesRepository;
+
     /**
      * @param Data $customShippingRateHelper
+     * @param ShipTableRatesRepositoryInterface $shipTableRatesRepository
      */
     public function __construct(
-        Data $customShippingRateHelper
+        Data $customShippingRateHelper,
+        ShipTableRatesRepositoryInterface $shipTableRatesRepository
     ) {
         $this->customShippingRateHelper = $customShippingRateHelper;
+        $this->shipTableRatesRepository = $shipTableRatesRepository;
     }
 
     /**
@@ -54,7 +63,7 @@ class ShippingPlugin
 
         if (!$this->customShippingRateHelper->isEnabled($storeId)
             || $address->getAddressType() != Address::ADDRESS_TYPE_SHIPPING
-            || strpos((string) $method, Carrier::CODE) === false
+            || (strpos((string) $method, Carrier::CODE) === false && strpos((string) $method, 'freeshipping') === false)
         ) {
             return $proceed($quote, $shippingAssignment, $total);
         }
@@ -66,6 +75,12 @@ class ShippingPlugin
             $shipping->setMethod($customShippingOption['code']);
             $address->setShippingMethod($customShippingOption['code']);
             $this->updateCustomRate($address, $customShippingOption);
+
+            if (strpos($method, 'freeshipping') === 0) {
+                $total->setShippingAmount(0);
+                $total->setBaseShippingAmount(0);
+                $total->setShippingDescription($customShippingOption['description'] ?? 'Free Shipping');
+            }
         }
 
         return $proceed($quote, $shippingAssignment, $total);
@@ -108,11 +123,20 @@ class ShippingPlugin
                 $rate = $selectedRate->getPrice();
             }
 
-            $jsonToArray = [
-                'code' => $json,
-                'type' => $this->customShippingRateHelper->getShippingCodeFromMethod($json, $storeId),
-                'rate' => $rate
-            ];
+            if (strpos($json, 'freeshipping') === 0) {
+                $jsonToArray = [
+                    'code' => $json,
+                    'type' => 'free_shipping',
+                    'rate' => 0,
+                    'description' => 'Free Shipping'
+                ];
+            } else {
+                $jsonToArray = [
+                    'code' => $json,
+                    'type' => $this->customShippingRateHelper->getShippingCodeFromMethod($json, $storeId),
+                    'rate' => $rate
+                ];
+            }
 
             return $this->formatShippingArray($jsonToArray);
         }
diff --git a/app/code/Coditron/CustomShippingRate/Plugin/Shipping/Model/Carrier/FreeShippingPlugin.php b/app/code/Coditron/CustomShippingRate/Plugin/Shipping/Model/Carrier/FreeShippingPlugin.php
new file mode 100644
index *********..c4272e4a5
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/Plugin/Shipping/Model/Carrier/FreeShippingPlugin.php
@@ -0,0 +1,149 @@
+<?php
+/**
+ * Copyright © Coditron Technologies All rights reserved.
+ * See COPYING.txt for license details.
+ * http://www.coditron.com | <EMAIL>
+ */
+
+namespace Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier;
+
+use Magento\OfflineShipping\Model\Carrier\Freeshipping;
+use Magento\Quote\Model\Quote\Address\RateRequest;
+use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;
+
+/**
+ * Plugin to conditionally enable core freeshipping when thresholds exist and are met
+ */
+class FreeShippingPlugin
+{
+    /**
+     * @var ShipTableRatesCollectionFactory
+     */
+    protected $shipTableRatesCollectionFactory;
+
+    /**
+     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
+     */
+    public function __construct(
+        ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
+    ) {
+        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
+    }
+
+    /**
+     * Enable core freeshipping only when thresholds exist and are met for the country
+     *
+     * @param Freeshipping $subject
+     * @param callable $proceed
+     * @param RateRequest $request
+     * @return \Magento\Shipping\Model\Rate\Result|bool
+     */
+    public function aroundCollectRates(Freeshipping $subject, callable $proceed, RateRequest $request)
+    {
+        $country = $request->getDestCountryId();
+        $subtotal = $request->getBaseSubtotalInclTax();
+
+        $metThresholds = $this->getMetThresholdsForCountry($country, $subtotal);
+
+        if (empty($metThresholds)) {
+            return false;
+        }
+
+        $this->overrideFreeShippingConfig($subject, $metThresholds);
+
+        $result = $proceed($request);
+
+        if ($result && $result->getAllRates()) {
+            $this->addThresholdSubtitle($result, $metThresholds);
+        }
+
+        return $result;
+    }
+
+    /**
+     * Get free shipping thresholds for the given country that are met
+     *
+     * @param string $country
+     * @param float $subtotal
+     * @return array
+     */
+    protected function getMetThresholdsForCountry($country, $subtotal)
+    {
+        if (!$country) {
+            return [];
+        }
+
+        $collection = $this->shipTableRatesCollectionFactory->create();
+        $collection->addFieldToFilter('free_shipping', 1)
+                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
+                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal]);
+
+        $metThresholds = [];
+        foreach ($collection as $threshold) {
+            $countries = $threshold->getCountries();
+            if (in_array($country, $countries)) {
+                $metThresholds[] = $threshold;
+            }
+        }
+
+        return $metThresholds;
+    }
+
+    /**
+     * Override free shipping configuration to enable it
+     *
+     * @param Freeshipping $subject
+     * @param array $metThresholds
+     * @return void
+     */
+    protected function overrideFreeShippingConfig(Freeshipping $subject, $metThresholds)
+    {
+        $lowestThreshold = null;
+        foreach ($metThresholds as $threshold) {
+            if ($lowestThreshold === null || $threshold->getMinOrderAmount() < $lowestThreshold) {
+                $lowestThreshold = $threshold->getMinOrderAmount();
+            }
+        }
+
+        $reflection = new \ReflectionClass($subject);
+        if ($reflection->hasProperty('_configData')) {
+            $configProperty = $reflection->getProperty('_configData');
+            $configProperty->setAccessible(true);
+            $configData = $configProperty->getValue($subject) ?: [];
+
+            $configData['free_shipping_subtotal'] = $lowestThreshold;
+            $configProperty->setValue($subject, $configData);
+        }
+    }
+
+    /**
+     * Add threshold subtitle to free shipping method
+     *
+     * @param \Magento\Shipping\Model\Rate\Result $result
+     * @param array $metThresholds
+     * @return void
+     */
+    protected function addThresholdSubtitle($result, $metThresholds)
+    {
+        if (empty($metThresholds)) {
+            return;
+        }
+
+        $lowestThreshold = null;
+        foreach ($metThresholds as $threshold) {
+            if ($lowestThreshold === null || $threshold->getMinOrderAmount() < $lowestThreshold) {
+                $lowestThreshold = $threshold->getMinOrderAmount();
+            }
+        }
+
+        foreach ($result->getAllRates() as $rate) {
+            if ($rate->getCarrier() === 'freeshipping') {
+                $currentTitle = $rate->getMethodTitle();
+                $subtitle = sprintf('(For orders over $%.2f)', $lowestThreshold);
+
+                $newTitle = $currentTitle . ' ' . $subtitle;
+                $rate->setMethodTitle($newTitle);
+            }
+        }
+    }
+}
diff --git a/app/code/Coditron/CustomShippingRate/Ui/Component/Listing/Column/ThresholdActions.php b/app/code/Coditron/CustomShippingRate/Ui/Component/Listing/Column/ThresholdActions.php
new file mode 100644
index *********..2a0a0b7c5
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/Ui/Component/Listing/Column/ThresholdActions.php
@@ -0,0 +1,48 @@
+<?php
+/**
+ * Actions column for Free Shipping Threshold grid
+ */
+declare(strict_types=1);
+
+namespace Coditron\CustomShippingRate\Ui\Component\Listing\Column;
+
+class ThresholdActions extends ShipTableRatesActions
+{
+
+    /**
+     * Prepare Data Source
+     *
+     * @param array $dataSource
+     * @return array
+     */
+    public function prepareDataSource(array $dataSource)
+    {
+        if (isset($dataSource['data']['items'])) {
+            foreach ($dataSource['data']['items'] as &$item) {
+                $name = $this->getData('name');
+                if (isset($item['shiptablerates_id'])) {
+                    $item[$name]['edit'] = [
+                        'href' => $this->urlBuilder->getUrl(
+                            'coditron_customshippingrate/shiptablerates/edit',
+                            ['shiptablerates_id' => $item['shiptablerates_id'], 'is_threshold' => 1]
+                        ),
+                        'label' => __('Edit')
+                    ];
+                    $item[$name]['delete'] = [
+                        'href' => $this->urlBuilder->getUrl(
+                            static::URL_PATH_DELETE,
+                            ['shiptablerates_id' => $item['shiptablerates_id']]
+                        ),
+                        'label' => __('Delete'),
+                        'confirm' => [
+                            'title' => __('Delete Threshold'),
+                            'message' => __('Are you sure you want to delete this free shipping threshold?')
+                        ]
+                    ];
+                }
+            }
+        }
+
+        return $dataSource;
+    }
+}
diff --git a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
index 12f42277c..b76c8f100 100644
--- a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
+++ b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ShippingRateListDataProvider.php
@@ -55,6 +55,9 @@ class ShippingRateListDataProvider extends \Magento\Ui\DataProvider\AbstractData
         $this->collection->addFieldToFilter(
             'seller_id',
             ['eq' => $this->helper->getSellerId()]
+        )->addFieldToFilter(
+            'min_order_amount',
+            ['eq' => 0]
         );
     }
 }
diff --git a/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ThresholdListDataProvider.php b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ThresholdListDataProvider.php
new file mode 100644
index *********..2a17e7e0f
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/Ui/DataProvider/ThresholdListDataProvider.php
@@ -0,0 +1,97 @@
+<?php
+/**
+ * Data provider for Free Shipping Threshold listing
+ */
+declare(strict_types=1);
+
+namespace Coditron\CustomShippingRate\Ui\DataProvider;
+
+use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
+use Magento\Framework\Api\Filter;
+use Magento\Ui\DataProvider\AbstractDataProvider;
+use Webkul\Marketplace\Helper\Data as MarketplaceHelper;
+
+class ThresholdListDataProvider extends AbstractDataProvider
+{
+    /**
+     * @var MarketplaceHelper
+     */
+    protected $marketplaceHelper;
+
+    /**
+     * @var array
+     */
+    protected $loadedData;
+
+    /**
+     * @param string $name
+     * @param string $primaryFieldName
+     * @param string $requestFieldName
+     * @param CollectionFactory $collectionFactory
+     * @param MarketplaceHelper $marketplaceHelper
+     * @param array $meta
+     * @param array $data
+     */
+    public function __construct(
+        $name,
+        $primaryFieldName,
+        $requestFieldName,
+        CollectionFactory $collectionFactory,
+        MarketplaceHelper $marketplaceHelper,
+        array $meta = [],
+        array $data = []
+    ) {
+        $this->collection = $collectionFactory->create();
+        $this->marketplaceHelper = $marketplaceHelper;
+        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
+    }
+
+    /**
+     * Get data
+     *
+     * @return array
+     */
+    public function getData()
+    {
+        if (isset($this->loadedData)) {
+            return $this->loadedData;
+        }
+
+        $sellerId = $this->marketplaceHelper->getCustomerId();
+
+        // Filter collection to show only records with min_order_amount > 0 for current seller
+        $this->collection->addFieldToFilter('seller_id', $sellerId)
+                        ->addFieldToFilter('min_order_amount', ['gt' => 0]);
+
+        $this->loadedData = $this->collection->toArray();
+        return $this->loadedData;
+    }
+
+    /**
+     * Add filter
+     *
+     * @param Filter $filter
+     * @return void
+     */
+    public function addFilter(Filter $filter)
+    {
+        if ($filter->getField() !== 'fulltext') {
+            $this->collection->addFieldToFilter(
+                $filter->getField(),
+                [$filter->getConditionType() => $filter->getValue()]
+            );
+        } else {
+            $value = trim($filter->getValue());
+            $this->collection->addFieldToFilter(
+                [
+                    ['attribute' => 'countries'],
+                    ['attribute' => 'min_order_amount']
+                ],
+                [
+                    ['like' => "%{$value}%"],
+                    ['like' => "%{$value}%"]
+                ]
+            );
+        }
+    }
+}
diff --git a/app/code/Coditron/CustomShippingRate/etc/db_schema.xml b/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
index 68559032e..f1710aec1 100644
--- a/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
+++ b/app/code/Coditron/CustomShippingRate/etc/db_schema.xml
@@ -21,6 +21,7 @@
         <column name="weight" nullable="true" xsi:type="varchar" comment="Weight (Up to, in Kg)" length="255"/>
         <column name="shipping_price" nullable="true" xsi:type="varchar" comment="Shipping price" length="255"/>
         <column name="free_shipping" nullable="false" xsi:type="boolean" default="false" comment="Free Shipping"/>
+        <column name="min_order_amount" nullable="true" xsi:type="decimal" precision="12" scale="4" default="0.0000" comment="Minimum Order Amount for Free Shipping"/>
 		<column name="seller_id" xsi:type="int" unsigned="true" nullable="false" identity="false" padding="10" default="0" comment="Seller Id"/>
 		<constraint xsi:type="foreign" referenceId="FK_CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" table="coditron_customshippingrate_shiptablerates" column="seller_id" referenceTable="customer_entity" referenceColumn="entity_id" onDelete="CASCADE"/>
         <index referenceId="CODITRON_CUSTOMSHIPPINGRATE_SHIPTABLERATES_SELLER_ID" indexType="btree">
diff --git a/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json b/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
index 3d5012378..c9b75af9f 100644
--- a/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
+++ b/app/code/Coditron/CustomShippingRate/etc/db_schema_whitelist.json
@@ -22,6 +22,7 @@
             "weight": true,
             "shipping_price": true,
             "free_shipping": true,
+            "min_order_amount": true,
             "seller_id": true
         },
         "index": {
diff --git a/app/code/Coditron/CustomShippingRate/etc/di.xml b/app/code/Coditron/CustomShippingRate/etc/di.xml
index 68c6a903c..96187ef7f 100644
--- a/app/code/Coditron/CustomShippingRate/etc/di.xml
+++ b/app/code/Coditron/CustomShippingRate/etc/di.xml
@@ -6,6 +6,9 @@
 	<type name="Magento\Checkout\Model\ShippingInformationManagement">
 		<plugin name="update_shipping_data_on_method_selection" type="Coditron\CustomShippingRate\Plugin\UpdateShippingDataOnMethodSelection" />
 	</type>
+	<type name="Magento\OfflineShipping\Model\Carrier\Freeshipping">
+		<plugin name="disable_core_freeshipping_when_thresholds_exist" type="Coditron\CustomShippingRate\Plugin\Shipping\Model\Carrier\FreeShippingPlugin" />
+	</type>
 	<preference for="Coditron\CustomShippingRate\Api\CustomShippingInformationManagementInterface" type="Coditron\CustomShippingRate\Model\CustomShippingInformationManagement" />
 	<preference for="Coditron\CustomShippingRate\Api\Data\CustomShippingInformationInterface"
                 type="Coditron\CustomShippingRate\Model\Data\CustomShippingInformation" />
diff --git a/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml b/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml
index 63e68d639..43c4b463d 100755
--- a/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/adminhtml/templates/order/create/shipping/method/form.phtml
@@ -40,8 +40,40 @@ $taxHelper = $block->getData('taxHelper');
                                 id="s_method_<?= $block->escapeHtmlAttr($_code) ?>" <?= /* @noEscape */ $_checked ?>
                                                                  class="admin__control-radio required-entry"/>
                             <label class="admin__field-label" for="s_method_<?= $block->escapeHtmlAttr($_code) ?>">
-                                <?= $block->escapeHtml($_rate->getMethodTitle() ?
-                                    $_rate->getMethodTitle() : $_rate->getMethodDescription()) ?> -
+                                <?php
+                                $listMethodTitle = $_rate->getMethodTitle() ? $_rate->getMethodTitle() : $_rate->getMethodDescription();
+
+                                if ($_rate->getCarrier() === 'freeshipping' && strpos($listMethodTitle, '(For orders over') === false) {
+                                    $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
+                                    $shipTableRatesCollectionFactory = $objectManager->get(\Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory::class);
+
+                                    $quote = $block->getQuote();
+                                    $country = $quote->getShippingAddress()->getCountryId();
+                                    $subtotal = $quote->getBaseSubtotalWithDiscount();
+
+                                    if ($country && $subtotal > 0) {
+                                        $collection = $shipTableRatesCollectionFactory->create();
+                                        $collection->addFieldToFilter('free_shipping', 1)
+                                                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
+                                                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal]);
+
+                                        $lowestThreshold = null;
+                                        foreach ($collection as $threshold) {
+                                            $countries = $threshold->getCountries();
+                                            if (in_array($country, $countries)) {
+                                                if ($lowestThreshold === null || $threshold->getMinOrderAmount() < $lowestThreshold) {
+                                                    $lowestThreshold = $threshold->getMinOrderAmount();
+                                                }
+                                            }
+                                        }
+
+                                        if ($lowestThreshold !== null) {
+                                            $listMethodTitle .= sprintf(' (For orders over $%.2f)', $lowestThreshold);
+                                        }
+                                    }
+                                }
+                                ?>
+                                <?= $block->escapeHtml($listMethodTitle) ?> -
                                 <strong>
                                     <?php $_excl = $block->getShippingPrice(
                                         $_rate->getPrice(),
@@ -111,7 +143,41 @@ $taxHelper = $block->getData('taxHelper');
                     <?= $block->escapeHtml($block->getCarrierName($_rate->getCarrier())) ?>
                 </dt>
                 <dd class="admin__order-shipment-methods-options">
-                    <?= $block->escapeHtml($_rate->getMethodTitle() ? $_rate->getMethodTitle() : $_rate->getMethodDescription()) ?> -
+                    <?php
+                    $methodTitle = $_rate->getMethodTitle() ? $_rate->getMethodTitle() : $_rate->getMethodDescription();
+
+                    // Add threshold subtitle for free shipping if not already present
+                    if ($_rate->getCarrier() === 'freeshipping' && strpos($methodTitle, '(For orders over') === false) {
+                        $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
+                        $shipTableRatesCollectionFactory = $objectManager->get(\Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory::class);
+
+                        $quote = $block->getQuote();
+                        $country = $quote->getShippingAddress()->getCountryId();
+                        $subtotal = $quote->getBaseSubtotalWithDiscount();
+
+                        if ($country && $subtotal > 0) {
+                            $collection = $shipTableRatesCollectionFactory->create();
+                            $collection->addFieldToFilter('free_shipping', 1)
+                                       ->addFieldToFilter('min_order_amount', ['gt' => 0])
+                                       ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal]);
+
+                            $lowestThreshold = null;
+                            foreach ($collection as $threshold) {
+                                $countries = $threshold->getCountries();
+                                if (in_array($country, $countries)) {
+                                    if ($lowestThreshold === null || $threshold->getMinOrderAmount() < $lowestThreshold) {
+                                        $lowestThreshold = $threshold->getMinOrderAmount();
+                                    }
+                                }
+                            }
+
+                            if ($lowestThreshold !== null) {
+                                $methodTitle .= sprintf(' (For orders over $%.2f)', $lowestThreshold);
+                            }
+                        }
+                    }
+                    ?>
+                    <?= $block->escapeHtml($methodTitle) ?> -
                     <strong>
                         <?php $_excl = $block->getShippingPrice(
                             $_rate->getPrice(),
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/layout/mpsellership_layout2_threshold_edit.xml b/app/code/Coditron/CustomShippingRate/view/frontend/layout/mpsellership_layout2_threshold_edit.xml
new file mode 100644
index *********..761c13ee4
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/layout/mpsellership_layout2_threshold_edit.xml
@@ -0,0 +1,17 @@
+<?xml version="1.0"?>
+
+<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="seller-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
+    <head>
+
+        <css src="Webkul_Marketplace::css/wk_block.css"/>
+        <css src="Webkul_Marketplace::css/style.css"/>
+        <css src="Webkul_Marketplace::css/product.css"/>
+        <css src="Webkul_Marketplace::css/layout.css"/>
+        <css src="Coditron_CustomShippingRate::css/select2/select2.css"/>
+    </head>
+    <body>
+        <referenceContainer name="seller.content">
+            <block class="Coditron\CustomShippingRate\Block\TableRates" name="mpsellership_threshold_edit" template="Coditron_CustomShippingRate::shiprate/edit_threshold.phtml" cacheable="false"></block>
+        </referenceContainer>
+    </body>
+</page>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml b/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
index 972c401ce..73e7e885b 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/layout/sellership_layout2_rates_manage.xml
@@ -20,6 +20,7 @@
         </referenceContainer>
         <referenceContainer name="sellership_rate_manage">
             <uiComponent name="sellership_rates_list_front"/>
+            <uiComponent name="sellership_threshold_list_front"/>
         </referenceContainer>
     </body>
 </page>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
index 3894ab393..a6b06a5e1 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/layout2/account/navigation.phtml
@@ -1,7 +1,7 @@
 <?php
      $marketplaceHelper = $block->getMpHelper();
      $isPartner= $marketplaceHelper->isSeller();
-     $isEnable = false;
+     $isEnable = $block->isenable();
      $magentoCurrentUrl = $block->getCurrentUrl();
 ?>
 <?php if ($isPartner): ?>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/edit_threshold.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/edit_threshold.phtml
new file mode 100644
index *********..b26904af9
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/edit_threshold.phtml
@@ -0,0 +1,114 @@
+<?php
+/** @var \Coditron\CustomShippingRate\Block\TableRates $block */
+$helper = $block->getMpHelper();
+$isPartner = $helper->isSeller();
+$backUrl = $block->getStoreUrl().'coditron_customshippingrate/shiptablerates/manage/';
+
+$sellerId = $block->getSellerId();
+$shipRate = $block->getShipRate();
+
+$countriesListHtml = $block->getCountries(true, $shipRate->getCountries());
+?>
+<div class="wk-mpsellercategory-container">
+    <?php if ($isPartner == 1): ?>
+        <form action="<?= $escaper->escapeUrl($block->getUrl('coditron_customshippingrate/shiptablerates/save')) ?>"
+        enctype="multipart/form-data" method="post" id="form-save-threshold"
+        data-mage-init='{"validation":{}}'>
+            <div class="fieldset wk-ui-component-container">
+                <?= $block->getBlockHtml('formkey') ?>
+                <?= $block->getBlockHtml('seller.formkey') ?>
+                <input type="hidden" name="id" value="<?= $escaper->escapeHtml($shipRate->getShiptableratesId()) ?>">
+                <input type="hidden" name="seller_id" value="<?= $escaper->escapeHtml($sellerId) ?>">
+                <input type="hidden" name="is_threshold" value="1">
+                
+                <!-- Set default values for threshold mode -->
+                <input type="hidden" name="courier_name" value="Free Shipping">
+                <input type="hidden" name="service_type" value="free_shipping">
+                <input type="hidden" name="weight" value="999999">
+                <input type="hidden" name="shipping_price" value="0">
+                <input type="hidden" name="free_shipping" value="1">
+                <input type="hidden" name="packing_time" value="0">
+                <input type="hidden" name="delivery_time" value="0">
+                <input type="hidden" name="total_lead_time" value="0">
+                <input type="hidden" name="return_address_id" value="0">
+                
+                <div class="page-main-actions">
+                    <div class="page-actions-placeholder"></div>
+                    <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                        <div class="page-actions-inner" data-title="<?= $escaper->escapeHtml(__("Free Shipping Threshold")); ?>">
+                            <div class="page-actions-buttons">
+                                <button id="back" title="<?= $escaper->escapeHtml(__("Back")); ?>" type="button"
+                                class="action- scalable back wk-ui-grid-btn-back wk-ui-grid-btn"
+                                data-ui-id="back-button">
+                                    <span><?= $escaper->escapeHtml(__("Back")); ?></span>
+                                </button>
+                                <button id="save"
+                                title="<?= $escaper->escapeHtml(__("Save Free Shipping Threshold")); ?>" type="submit"
+                                class="action- scalable save primary ui-button ui-widget
+                                ui-state-default ui-corner-all ui-button-text-only wk-ui-grid-btn
+                                wk-ui-grid-btn-primary"
+                                data-form-role="save"
+                                data-ui-id="save-button" role="button" aria-disabled="false">
+                                    <span class="ui-button-text">
+                                        <span><?= $escaper->escapeHtml(__("Save Free Shipping Threshold")); ?></span>
+                                    </span>
+                                </button>
+                            </div>
+                        </div>
+                    </div>
+                </div>
+                
+                <div class="field required">
+                    <label class="label" for="countries">
+                        <span><?php /* @escapeNotVerified */ echo __('Countries') ?></span>
+                    </label>
+                    <div class="tooltip">
+                    <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
+                    <span class="tooltiptext"><?= $escaper->escapeHtml(__('Select countries where free shipping applies')) ?></span>
+                    </div>
+                    <div class="control">
+                        <?php echo $countriesListHtml; ?>
+                    </div>
+                </div>
+                
+                <div class="field required">
+                    <label for="min_order_amount" class="label">
+                        <span><?= $escaper->escapeHtml(__("Minimum Order Amount (USD)")); ?></span>
+                    </label>
+                    <div class="tooltip">
+                     <span class="tooltipicon"><?= $escaper->escapeHtml(__('?')) ?></span>
+                     <span class="tooltiptext"><?= $escaper->escapeHtml(__('Minimum order amount required for free shipping')) ?></span>
+                     </div>
+                    <div class="control">
+                        <input type="text" class="input-text required-entry validate-number validate-zero-or-greater" 
+                        name="min_order_amount"
+                        data-validate="{required:true, 'validate-number':true, 'validate-zero-or-greater':true}" 
+                        title="<?= $escaper->escapeHtml(__("Minimum Order Amount")); ?>"
+                        id="min_order_amount" 
+                        value="<?= $escaper->escapeHtml($block->escapeHtml($shipRate->getMinOrderAmount())) ?>">
+                    </div>
+                </div>
+            </div>
+        </form>
+    <?php else: ?>
+        <h2 class="wk-mp-error-msg">
+            <?= $escaper->escapeHtml(__("To Become Seller Please Contact to Admin.")); ?>
+        </h2>
+    <?php endif; ?>
+</div>
+
+<script type="text/x-magento-init">
+{
+    "#form-save-threshold": {
+        "Coditron_CustomShippingRate/js/threshold-form": {
+            "backUrl": "<?= $escaper->escapeJs($backUrl) ?>"
+        }
+    }
+}
+</script>
+
+<style>
+    .select2-search__field {
+        height: auto !important;
+    }
+</style>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
index 15601be5f..351fae526 100644
--- a/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/templates/shiprate/list.phtml
@@ -10,21 +10,51 @@
  */
 ?>
 <div class="wk-mpsellercategory-container">
-    <div class="page-main-actions">
-        <div class="page-actions-placeholder"></div>
-        <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
-            <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
-                <div class="page-actions-buttons">
-                    <button id="add" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
-                    class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
-                    onclick="location.href
-                    = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
-                    data-ui-id="add-button">
-                        <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
-                    </button>
+    <!-- Shipping Methods Section -->
+    <div class="wk-mp-section">
+        <h3 class="wk-mp-section-title"><?= $escaper->escapeHtml(__('Shipping Methods')) ?></h3>
+        <div class="page-main-actions">
+            <div class="page-actions-placeholder"></div>
+            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
+                    <div class="page-actions-buttons">
+                        <button id="add-shipping-method" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
+                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
+                        onclick="location.href
+                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
+                        data-ui-id="add-button">
+                            <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
+                        </button>
+                    </div>
                 </div>
             </div>
         </div>
+        <div id="shipping-methods-grid">
+            <?= /* @noEscape */ $block->getChildHtml('sellership_rates_list_front'); ?>
+        </div>
+    </div>
+
+    <!-- Free Shipping Thresholds Section -->
+    <div class="wk-mp-section" style="margin-top: 40px;">
+        <h3 class="wk-mp-section-title"><?= $escaper->escapeHtml(__('Free Shipping Thresholds')) ?></h3>
+        <div class="page-main-actions">
+            <div class="page-actions-placeholder"></div>
+            <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
+                <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Free Shipping Threshold")); ?>'>
+                    <div class="page-actions-buttons">
+                        <button id="add-threshold" title='<?= $escaper->escapeHtml(__("Add New Threshold")); ?>' type="button"
+                        class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
+                        onclick="location.href
+                        = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new', ['is_threshold' => 1]))?>';"
+                        data-ui-id="add-threshold-button">
+                            <span><?= $escaper->escapeHtml(__("Add New Threshold")); ?></span>
+                        </button>
+                    </div>
+                </div>
+            </div>
+        </div>
+        <div id="threshold-grid">
+            <?= /* @noEscape */ $block->getChildHtml('sellership_threshold_list_front'); ?>
+        </div>
     </div>
-    <?= /* @noEscape */ $block->getChildHtml(); ?>
 </div>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/ui_component/sellership_threshold_list_front.xml b/app/code/Coditron/CustomShippingRate/view/frontend/ui_component/sellership_threshold_list_front.xml
new file mode 100644
index *********..f52c8a21b
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/ui_component/sellership_threshold_list_front.xml
@@ -0,0 +1,119 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<!--
+/**
+ * Free Shipping Threshold UI Component for Seller Dashboard
+ */
+-->
+<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
+    <argument name="data" xsi:type="array">
+        <item name="js_config" xsi:type="array">
+            <item name="provider" xsi:type="string">sellership_threshold_list_front.sellership_threshold_list_front_data_source</item>
+            <item name="deps" xsi:type="string">sellership_threshold_list_front.sellership_threshold_list_front_data_source</item>
+        </item>
+        <item name="spinner" xsi:type="string">sellership_threshold_columns</item>
+    </argument>
+    <dataSource name="sellership_threshold_list_front_data_source">
+        <argument name="dataProvider" xsi:type="configurableObject">
+            <argument name="class" xsi:type="string">Coditron\CustomShippingRate\Ui\DataProvider\ThresholdListDataProvider</argument>
+            <argument name="name" xsi:type="string">sellership_threshold_list_front_data_source</argument>
+            <argument name="primaryFieldName" xsi:type="string">shiptablerates_id</argument>
+            <argument name="requestFieldName" xsi:type="string">id</argument>
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="component" xsi:type="string">Webkul_Marketplace/js/grid/provider</item>
+                    <item name="update_url" xsi:type="url" path="marketplace/mui_index/render"/>
+                    <item name="storageConfig" xsi:type="array">
+                        <item name="cacheRequests" xsi:type="boolean">false</item>
+                    </item>
+                </item>
+            </argument>
+        </argument>
+    </dataSource>
+    <listingToolbar name="listing_top">
+        <columnsControls name="columns_controls"/>
+        <filters name="listing_filters">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="statefull" xsi:type="array">
+                        <item name="applied" xsi:type="boolean">false</item>
+                    </item>
+                    <item name="params" xsi:type="array">
+                        <item name="filters_modifier" xsi:type="array" />
+                    </item>
+                </item>
+            </argument>
+        </filters>
+        <massaction name="listing_massaction">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="selectProvider" xsi:type="string">sellership_threshold_list_front.sellership_threshold_list_front.sellership_threshold_columns.ids</item>
+                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
+                </item>
+            </argument>
+            <action name="delete">
+                <argument name="data" xsi:type="array">
+                    <item name="config" xsi:type="array">
+                        <item name="type" xsi:type="string">delete</item>
+                        <item name="label" xsi:type="string" translate="true">Delete</item>
+                        <item name="url" xsi:type="url" path="coditron_customshippingrate/shiptablerates/delete"/>
+                        <item name="confirm" xsi:type="array">
+                            <item name="title" xsi:type="string" translate="true">Delete Threshold</item>
+                            <item name="message" xsi:type="string" translate="true">Are you sure you want to delete selected free shipping thresholds?</item>
+                        </item>
+                    </item>
+                </argument>
+            </action>
+        </massaction>
+        <paging name="listing_paging"/>
+    </listingToolbar>
+    <columns name="sellership_threshold_columns">
+        <argument name="data" xsi:type="array">
+            <item name="config" xsi:type="array">
+                <item name="childDefaults" xsi:type="array">
+                    <item name="fieldAction" xsi:type="array">
+                        <item name="provider" xsi:type="string">thresholdGrid</item>
+                        <item name="target" xsi:type="string">selectThreshold</item>
+                        <item name="params" xsi:type="array">
+                            <item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
+                        </item>
+                    </item>
+                </item>
+            </item>
+        </argument>
+        <selectionsColumn name="ids">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
+                </item>
+            </argument>
+        </selectionsColumn>
+        <column name="countries">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="filter" xsi:type="string">text</item>
+                    <item name="label" xsi:type="string" translate="true">Countries</item>
+                    <item name="sortOrder" xsi:type="number">20</item>
+                </item>
+            </argument>
+        </column>
+        <column name="min_order_amount">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="filter" xsi:type="string">text</item>
+                    <item name="label" xsi:type="string" translate="true">Minimum Order Amount (USD)</item>
+                    <item name="sortOrder" xsi:type="number">30</item>
+                </item>
+            </argument>
+        </column>
+        <actionsColumn name="actions" class="Coditron\CustomShippingRate\Ui\Component\Listing\Column\ThresholdActions">
+            <argument name="data" xsi:type="array">
+                <item name="config" xsi:type="array">
+                    <item name="indexField" xsi:type="string">shiptablerates_id</item>
+                    <item name="viewUrlPath" xsi:type="string">coditron_customshippingrate/shiptablerates/editthreshold</item>
+                    <item name="urlEntityParamName" xsi:type="string">id</item>
+                    <item name="sortOrder" xsi:type="number">100</item>
+                </item>
+            </argument>
+        </actionsColumn>
+    </columns>
+</listing>
diff --git a/app/code/Coditron/CustomShippingRate/view/frontend/web/js/threshold-form.js b/app/code/Coditron/CustomShippingRate/view/frontend/web/js/threshold-form.js
new file mode 100644
index *********..36ed42380
--- /dev/null
+++ b/app/code/Coditron/CustomShippingRate/view/frontend/web/js/threshold-form.js
@@ -0,0 +1,16 @@
+define(['jquery', 'select2'], function($) {
+    'use strict';
+
+    return function(config, element) {
+        $(document).ready(function() {
+            $("#back").click(function(){
+                window.location.replace(config.backUrl);
+            });
+
+            $('.custom-multiselect').select2({
+                placeholder: "Select countries",
+                allowClear: true
+            });
+        });
+    };
+});
diff --git a/app/code/Comave/BigBuy/Service/Order/SynchroniseService.php b/app/code/Comave/BigBuy/Service/Order/SynchroniseService.php
index 9540e7c51..9a5e03ea2 100644
--- a/app/code/Comave/BigBuy/Service/Order/SynchroniseService.php
+++ b/app/code/Comave/BigBuy/Service/Order/SynchroniseService.php
@@ -340,13 +340,21 @@ class SynchroniseService implements OrderSynchroniseInterface
                 );
                 $this->notificationService->addMessage((int)$order->getEntityId(), $message);
             } else {
-                $message = match ((int)$result['code']) {
-                    400 => 'Validation errors has been found when uploading the invoice.',
-                    409 => 'There are constrain conflicts.',
-                    415 => 'Invalid Content-Type header.',
-                    429 => 'Exceeded requests limits.',
-                    default => 'There was an error uploading the invoice for the order.',
-                };
+                $message = 'There was an error uploading the invoice for the order.';
+                switch ((int)$result['code']) {
+                    case 400:
+                        $message = 'Validation errors has been found when uploading the invoice.';
+                        break;
+                    case 409:
+                        $message = 'There are constrain conflicts.';
+                        break;
+                    case 415:
+                        $message = 'Invalid Content-Type header.';
+                        break;
+                    case 429:
+                        $message = 'Exceeded requests limits.';
+                        break;
+                }
                 if (!empty($result['message'])) {
                     $message = sprintf("%s %s", $message, $result['message']);
                 }
diff --git a/app/code/Comave/CategoryCommission/Api/Data/CommissionInterface.php b/app/code/Comave/CategoryCommission/Api/Data/CommissionInterface.php
index 732baf1ac..74e0e60af 100644
--- a/app/code/Comave/CategoryCommission/Api/Data/CommissionInterface.php
+++ b/app/code/Comave/CategoryCommission/Api/Data/CommissionInterface.php
@@ -63,7 +63,7 @@ interface CommissionInterface extends \Magento\Framework\Api\ExtensibleDataInter
 
     /**
      * Set type
-     * @param int $value
+     * @param string $value
      * @return \Comave\CategoryCommission\Api\Data\CommissionInterface
      */
     public function setValue($value);
diff --git a/app/code/Comave/CategoryCommission/Controller/Adminhtml/Export/Download.php b/app/code/Comave/CategoryCommission/Controller/Adminhtml/Export/Download.php
index 1bfbdbfbd..c9e57c58e 100644
--- a/app/code/Comave/CategoryCommission/Controller/Adminhtml/Export/Download.php
+++ b/app/code/Comave/CategoryCommission/Controller/Adminhtml/Export/Download.php
@@ -13,6 +13,7 @@ use Magento\Framework\Api\SearchCriteriaBuilder;
 use Magento\Framework\Filesystem\Io\File;
 use Magento\Framework\Convert\ConvertArray;
 use Magento\Framework\App\ResponseInterface;
+use Comave\CategoryCommission\Api\Data\CommissionSearchResultsInterface;
 
 class Download extends Action
 {
diff --git a/app/code/Comave/CategoryCommission/Controller/Adminhtml/Index/MassDelete.php b/app/code/Comave/CategoryCommission/Controller/Adminhtml/Index/MassDelete.php
deleted file mode 100644
index b030094cc..*********
--- a/app/code/Comave/CategoryCommission/Controller/Adminhtml/Index/MassDelete.php
+++ /dev/null
@@ -1,51 +0,0 @@
-<?php
-/**
- * Copyright © Commercial Avenue
- */
-namespace Comave\CategoryCommission\Controller\Adminhtml\Index;
-
-use Magento\Framework\App\Action\HttpPostActionInterface;
-use Magento\Framework\Controller\ResultFactory;
-use Magento\Backend\App\Action\Context;
-use Magento\Ui\Component\MassAction\Filter;
-use Comave\CategoryCommission\Model\ResourceModel\Commission\CollectionFactory;
-
-class MassDelete extends \Magento\Backend\App\Action implements HttpPostActionInterface
-{
-    public const string ADMIN_RESOURCE = 'Comave_CategoryCommission::import_export';
-
-    /**
-     * @param Context $context
-     * @param Filter $filter
-     * @param CollectionFactory $collectionFactory
-     */
-    public function __construct(
-        Context                   $context,
-        private Filter            $filter,
-        private CollectionFactory $collectionFactory
-    ) {
-        parent::__construct($context);
-    }
-
-    /**
-     * Execute action
-     *
-     * @return \Magento\Backend\Model\View\Result\Redirect
-     * @throws \Magento\Framework\Exception\LocalizedException|\Exception
-     */
-    public function execute()
-    {
-        $collection = $this->filter->getCollection($this->collectionFactory->create());
-        $collectionSize = $collection->getSize();
-
-        foreach ($collection as $commission) {
-            $commission->delete();
-        }
-
-        $this->messageManager->addSuccess(__('A total of %1 record(s) have been deleted.', $collectionSize));
-
-        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
-        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
-        return $resultRedirect->setPath('*/*/');
-    }
-}
diff --git a/app/code/Comave/CategoryCommission/Plugin/Catalog/Model/Category/PluginLoadCategory.php b/app/code/Comave/CategoryCommission/Plugin/Catalog/Model/Category/PluginLoadCategory.php
index e3fd4709e..1fbff92b5 100644
--- a/app/code/Comave/CategoryCommission/Plugin/Catalog/Model/Category/PluginLoadCategory.php
+++ b/app/code/Comave/CategoryCommission/Plugin/Catalog/Model/Category/PluginLoadCategory.php
@@ -33,37 +33,14 @@ class PluginLoadCategory
             $entity->getName() ?: ''
         );
         if (isset($commission[0])){
-            $subject->setData('commission_type', $commission[0]->getType());
-            $subject->setData('commission_value', $commission[0]->getValue());
+            $entity->setData('commission_type', $commission[0]->getType());
+            $entity->setData('commission_value', $commission[0]->getValue());
         }
-        $extensionAttributes = $subject->getExtensionAttributes();
+        $extensionAttributes = $entity->getExtensionAttributes();
         /** get current extension attributes from entity **/
         $extensionAttributes->setCommission($commission);
-        $subject->setExtensionAttributes($extensionAttributes);
+        $entity->setExtensionAttributes($extensionAttributes);
 
-        return $subject;
-    }
-
-    /**
-     * @param \Magento\Catalog\Api\Data\CategoryInterface $subject
-     * @param \Magento\Catalog\Api\Data\CategoryInterface $entity
-     * @return \Magento\Catalog\Api\Data\CategoryInterface
-     */
-    public function afterSave(\Magento\Catalog\Api\Data\CategoryInterface $subject, \Magento\Catalog\Api\Data\CategoryInterface $entity)
-    {
-        if(!$subject->getCommissionType() || !$subject->getCommissionValue()){
-            return $subject;
-        }
-        $commissions = $subject->getExtensionAttributes()->getCommission();
-        foreach ($commissions as $commission) {
-            if($commission->getStoreId() == $subject->getStoreId()){
-                $commission->setType($subject->getCommissionType());
-                $commission->setValue($subject->getCommissionValue());
-
-                $commission->setStoreId($subject->getStoreId());
-                $this->commissionRepository->save($commission);
-            }
-        }
-        return $subject;
+        return $entity;
     }
 }
diff --git a/app/code/Comave/CategoryCommission/view/adminhtml/ui_component/category_form.xml b/app/code/Comave/CategoryCommission/view/adminhtml/ui_component/category_form.xml
index 74ea80289..e14024ba1 100644
--- a/app/code/Comave/CategoryCommission/view/adminhtml/ui_component/category_form.xml
+++ b/app/code/Comave/CategoryCommission/view/adminhtml/ui_component/category_form.xml
@@ -21,7 +21,7 @@
                     <item name="dataType" xsi:type="string">text</item>
                     <item name="formElement" xsi:type="string">input</item>
                     <item name="label" xsi:type="string" translate="true">Commission Value</item>
-                    <item name="default" xsi:type="number">10</item>
+                    <item name="default" xsi:type="number">0</item>
                 </item>
             </argument>
         </field>
diff --git a/app/code/Comave/CategoryCommission/view/adminhtml/ui_component/commission_listing.xml b/app/code/Comave/CategoryCommission/view/adminhtml/ui_component/commission_listing.xml
index f8c772d26..d93d84873 100644
--- a/app/code/Comave/CategoryCommission/view/adminhtml/ui_component/commission_listing.xml
+++ b/app/code/Comave/CategoryCommission/view/adminhtml/ui_component/commission_listing.xml
@@ -86,29 +86,6 @@
                 </settings>
             </filterSelect>
         </filters>
-        <massaction name="listing_massaction">
-            <argument name="data" xsi:type="array">
-                <item name="config" xsi:type="array">
-                    <item name="selectProvider" xsi:type="string">commission_listing.commission_listing.commission_columns.ids</item>
-                    <item name="indexField" xsi:type="string">ids</item>
-                </item>
-            </argument>
-            <action name="delete">
-                <argument name="data" xsi:type="array">
-                    <item name="config" xsi:type="array">
-                        <item name="type" xsi:type="string">delete</item>
-                        <item name="label" xsi:type="string" translate="true">Delete</item>
-                        <item name="url" xsi:type="url" path="commission/index/massDelete"/>
-                        <item name="confirm" xsi:type="array">
-                            <item name="title" xsi:type="string" translate="true">Delete items</item>
-                            <item name="message" xsi:type="string" translate="true">Are you sure you wan't to delete selected items?</item>
-                        </item>
-                    </item>
-                </argument>
-            </action>
-
-        </massaction>
-
         <paging name="listing_paging"/>
     </listingToolbar>
     <columns name="commission_columns">
diff --git a/app/code/Comave/Marketplace/Setup/Patch/Data/InstallProductMissingAttributes.php b/app/code/Comave/Marketplace/Setup/Patch/Data/InstallProductMissingAttributes.php
index b0ee63e0b..0ad0f4d2e 100644
--- a/app/code/Comave/Marketplace/Setup/Patch/Data/InstallProductMissingAttributes.php
+++ b/app/code/Comave/Marketplace/Setup/Patch/Data/InstallProductMissingAttributes.php
@@ -9,11 +9,8 @@ namespace Comave\Marketplace\Setup\Patch\Data;
 use Comave\Marketplace\Model\FixtureManager;
 use Magento\Catalog\Model\Product;
 use Magento\Eav\Setup\EavSetupFactory;
-use Magento\Framework\Exception\LocalizedException;
 use Magento\Framework\Setup\ModuleDataSetupInterface;
 use Magento\Framework\Setup\Patch\DataPatchInterface;
-use Magento\Framework\Validator\ValidateException;
-use Psr\Log\LoggerInterface;
 
 class InstallProductMissingAttributes implements DataPatchInterface
 {
@@ -25,8 +22,7 @@ class InstallProductMissingAttributes implements DataPatchInterface
     public function __construct(
         private readonly ModuleDataSetupInterface $moduleDataSetup,
         private readonly EavSetupFactory $eavSetupFactory,
-        private readonly FixtureManager $fixtureManager,
-        private readonly LoggerInterface $logger
+        private readonly FixtureManager $fixtureManager
     ) {
     }
 
@@ -40,22 +36,13 @@ class InstallProductMissingAttributes implements DataPatchInterface
 
     /**
      * @return void
+     * @throws \Magento\Framework\Exception\LocalizedException
+     * @throws \Magento\Framework\Validator\ValidateException
      */
     public function apply(): void
     {
         $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
-        try {
-            $attributes = $this->fixtureManager->getData('product-missing-attributes');
-        } catch (LocalizedException|ValidateException $e) {
-            $this->logger->warning(
-                'Unable to get attributes to be installed',
-                [
-                    'exception' => $e->getMessage(),
-                ]
-            );
-            $attributes = [];
-        }
-
+        $attributes = $this->fixtureManager->getData('product-missing-attributes');
         foreach ($attributes as $attributeData) {
             $code = $attributeData['attribute_code'];
             if (!$this->attributeExists($code)) {
diff --git a/app/code/Comave/ReturnAddress/view/frontend/templates/account/navigation.phtml b/app/code/Comave/ReturnAddress/view/frontend/templates/account/navigation.phtml
index fc11b62a3..fb988732f 100755
--- a/app/code/Comave/ReturnAddress/view/frontend/templates/account/navigation.phtml
+++ b/app/code/Comave/ReturnAddress/view/frontend/templates/account/navigation.phtml
@@ -1,27 +1,25 @@
 <?php
-/** @var \Magento\Framework\View\Element\Template $block */
-/** @var \Magento\Framework\Escaper $escaper */
-/** @var \Webkul\Marketplace\Helper\Data $_helper */
-$_helper = $this->helper(\Webkul\Marketplace\Helper\Data::class);
+$helperBlock = $block->getLayout()->createBlock(\Webkul\MpEasyPost\Block\EasyPostData::class);
+$_helper = $helperBlock->getMarketplaceHelper();
+$helper = $helperBlock->getHelper();
 $isPartner = $_helper->isSeller();
 $isSellerGroup = $_helper->isSellerGroupModuleInstalled();
-?>
-<?php if ($isPartner): ?>
-    <?php if (($isSellerGroup && $_helper->isAllowedAction('returnaddress/index/index')) || !$isSellerGroup): ?>
+if ($isPartner) { ?>
+    <?php if (($isSellerGroup && $_helper->isAllowedAction('returnaddress/index/index')) || !$isSellerGroup) { ?>
         <li data-ui-id="menu-webkul-marketplace-setting-return-address" class="item-menu parent level-1">
             <strong class="wk-mp-submenu-group-title">
-                <span><?= $escaper->escapeHtml(__('Returns')) ?></span>
+                <span><?= $block->escapeHtml(__('Returns')) ?></span>
             </strong>
             <div class="wk-mp-submenu">
                 <ul>
                     <li class="level-2">
-                        <a href="<?= $escaper->escapeUrl($block->getUrl('returnaddress', ['_secure' => $block->getRequest()->isSecure()])); ?>">
-                            <span><?= $escaper->escapeHtml(__('Return Address')) ?></span>
+                        <a href="<?= $block->escapeUrl($block->getUrl('returnaddress', ['_secure' => $block->getRequest()->isSecure()])); ?>">
+                            <span><?= $block->escapeHtml(__('Return Address')) ?></span>
                         </a>
                     </li>
                 </ul>
             </div>
         </li>
-    <?php endif; ?>
-<?php endif; ?>
+    <?php } ?>
+<?php } ?>
 
diff --git a/app/code/Comave/Rma/README.md b/app/code/Comave/Rma/README.md
index 6896f4f29..27cc36262 100644
--- a/app/code/Comave/Rma/README.md
+++ b/app/code/Comave/Rma/README.md
@@ -58,32 +58,7 @@ query {
 ---
 
 ## 3. Initiate a Return Request
-**Get Reason List**
-```graphql
-query {
-  customAttributeMetadataV2(
-    attributes: [
-      { attribute_code: "reason", entity_type: "rma_item" }
-    ]
-  ) {
-    items {
-      code
-      label
-      entity_type
-      frontend_input
-      is_required
-      options {
-        label
-        value #used to set the selected options below as base64_encoded
-      }
-    }
-    errors {
-      type
-      message
-    }
-  }
-}
-```
+
 **Mutation:**
 
 ```graphql
@@ -97,12 +72,6 @@ mutation {
         {
           order_item_uid: "ITEM_UID"
           quantity_to_return: 1
-          "selected_custom_attributes": [
-                {
-                    "attribute_code": "reason",
-                    "value": "ODE=" #base64 encoded string from the customAttributesV2 query above in the options
-                }
-            ]
         }
       ]
     }
@@ -117,16 +86,6 @@ mutation {
       }
       number
       status
-      items {
-        uid
-        custom_attributesV2 {
-            code
-            ... on AttributeSelectedOptions {
-                value
-                label
-            }
-        }
-      }
     }
   }
 }
diff --git a/app/code/Comave/RmaGraphQl/Plugin/ItemAttributeSave.php b/app/code/Comave/RmaGraphQl/Plugin/ItemAttributeSave.php
deleted file mode 100644
index 40aa084d7..*********
--- a/app/code/Comave/RmaGraphQl/Plugin/ItemAttributeSave.php
+++ /dev/null
@@ -1,57 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace Comave\RmaGraphQl\Plugin;
-
-use Magento\Framework\Model\AbstractModel;
-use Magento\Rma\Model\ResourceModel\Item;
-
-class ItemAttributeSave
-{
-    /**
-     * Seems there's a bug with the core that doesn't handle attributes properly for multiple items in a single request
-     * @param Item $itemResource
-     * @param callable $proceed
-     * @param AbstractModel $rmaItem
-     * @return Item
-     * @throws \Exception
-     */
-    public function aroundSave(
-        Item $itemResource,
-        callable $proceed,
-        AbstractModel $rmaItem
-    ): Item {
-        $reasonValue = $rmaItem->getReason();
-        $condition = $rmaItem->getCondition();
-        $resolution = $rmaItem->getResolution();
-
-        $result = $proceed($rmaItem);
-
-        if (!empty($condition)) {
-            $rmaItem->setCondition($condition);
-            $itemResource->saveAttribute(
-                $rmaItem,
-                'condition'
-            );
-        }
-
-        if (!empty($reasonValue)) {
-            $rmaItem->setReason($reasonValue);
-            $itemResource->saveAttribute(
-                $rmaItem,
-                'reason'
-            );
-        }
-
-        if (!empty($resolution)) {
-            $rmaItem->setResolution($resolution);
-            $itemResource->saveAttribute(
-                $rmaItem,
-                'resolution'
-            );
-        }
-
-        return $result;
-    }
-}
diff --git a/app/code/Comave/RmaGraphQl/Service/CustomOptionProvider.php b/app/code/Comave/RmaGraphQl/Service/CustomOptionProvider.php
deleted file mode 100644
index c8ea2ea89..*********
--- a/app/code/Comave/RmaGraphQl/Service/CustomOptionProvider.php
+++ /dev/null
@@ -1,48 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace Comave\RmaGraphQl\Service;
-
-use Magento\Eav\Model\ResourceModel\Entity\Attribute\OptionValueProvider;
-use Magento\Framework\App\ResourceConnection;
-use Magento\Framework\Data\OptionSourceInterface;
-use Magento\Framework\Phrase;
-
-class CustomOptionProvider extends OptionValueProvider
-{
-    /**
-     * @param ResourceConnection $connection
-     * @param OptionSourceInterface[] $newOptionsProviders
-     */
-    public function __construct(
-        ResourceConnection $connection,
-        private readonly array $newOptionsProviders
-    ) {
-        parent::__construct($connection);
-    }
-
-    /**
-     * @param int $optionId
-     * @return string|null
-     */
-    public function get(int $optionId): ?string
-    {
-        /** @var OptionSourceInterface $provider */
-        foreach ($this->newOptionsProviders as $provider) {
-            $options = $provider->toOptionArray();
-
-            foreach ($options as $option) {
-                if ((int) $option['value'] !== $optionId) {
-                    continue;
-                }
-
-                return $option['label'] instanceof Phrase ?
-                    $option['label']->render() :
-                    $option['label'];
-            }
-        }
-
-        return parent::get($optionId);
-    }
-}
diff --git a/app/code/Comave/RmaGraphQl/etc/graphql/di.xml b/app/code/Comave/RmaGraphQl/etc/graphql/di.xml
index 8c1f8a11d..7d73de32c 100644
--- a/app/code/Comave/RmaGraphQl/etc/graphql/di.xml
+++ b/app/code/Comave/RmaGraphQl/etc/graphql/di.xml
@@ -76,23 +76,4 @@
             <argument name="logger" xsi:type="object">ComaveSellerReturns</argument>
         </arguments>
     </type>
-
-    <type name="Magento\RmaGraphQl\Model\Rma\Item\Builder">
-        <arguments>
-            <argument xsi:type="object" name="optionValueProvider">Comave\RmaGraphQl\Service\CustomOptionProvider</argument>
-        </arguments>
-    </type>
-
-    <type name="Comave\RmaGraphQl\Service\CustomOptionProvider">
-        <arguments>
-            <argument xsi:type="array" name="newOptionsProviders">
-                <item name="reason" xsi:type="object">Comave\Rma\Model\Source\Reasons</item>
-                <item name="resolution" xsi:type="object">Comave\Rma\Model\Source\Resolution</item>
-            </argument>
-        </arguments>
-    </type>
-
-    <type name="Magento\Rma\Model\ResourceModel\Item">
-        <plugin name="ensureReasonSave" type="Comave\RmaGraphQl\Plugin\ItemAttributeSave"/>
-    </type>
 </config>
diff --git a/app/code/Comave/SellerApi/Model/IntegrationTypePool.php b/app/code/Comave/SellerApi/Model/IntegrationTypePool.php
index 6d1c49039..94a85551b 100644
--- a/app/code/Comave/SellerApi/Model/IntegrationTypePool.php
+++ b/app/code/Comave/SellerApi/Model/IntegrationTypePool.php
@@ -133,8 +133,7 @@ class IntegrationTypePool
         $dispatchedIntegration = $this->integrationPool[$integrationName];
         $dispatchedIntegration->setSellerId(
             $this->productIntegrations[$productId]['seller_id']
-        )->setIntegrationType($integrationName);
-        
+        );
         $this->eventManager->dispatch(
             'product_integration_assign',
             [
@@ -161,7 +160,7 @@ class IntegrationTypePool
             if ($integration->getIntegrationType() === self::NON_INTEGRATED) {
                 continue;
             }
-
+            
             if ($integrationType !== null && $integration->getIntegrationType() !== $integrationType) {
                 continue;
             }
diff --git a/app/code/Comave/ShopifyAccounts/Model/OrderSynchronizer.php b/app/code/Comave/ShopifyAccounts/Model/OrderSynchronizer.php
index 08086058e..6636c1573 100644
--- a/app/code/Comave/ShopifyAccounts/Model/OrderSynchronizer.php
+++ b/app/code/Comave/ShopifyAccounts/Model/OrderSynchronizer.php
@@ -17,11 +17,11 @@ use Magento\Sales\Api\Data\OrderInterface;
 use Magento\Sales\Api\Data\OrderItemInterface;
 use Magento\Sales\Api\OrderRepositoryInterface;
 use Magento\SalesRule\Api\RuleRepositoryInterface;
+use Magento\SalesRule\Model\Rule\Action\SimpleActionOptionsProvider;
 use Psr\Log\LoggerInterface;
+use Webkul\MpMultiShopifyStoreMageConnect\Api\Data\OrdermapInterface;
 use Webkul\MpMultiShopifyStoreMageConnect\Api\Data\OrdermapInterfaceFactory;
 use Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Ordermap;
-use Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Ordermap\Collection;
-use Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Ordermap\CollectionFactory;
 
 class OrderSynchronizer implements OrderSynchroniseInterface
 {
@@ -30,10 +30,9 @@ class OrderSynchronizer implements OrderSynchroniseInterface
      * @param OrderRepositoryInterface $orderRepository
      * @param ResourceConnection $resourceConnection
      * @param LoggerInterface $logger
-     * @param OrdermapInterfaceFactory $shopifyOrderFactory
-     * @param Ordermap $resource
      * @param SerializerInterface $serializer
-     * @param CollectionFactory $collectionFactory
+     * @param OrdermapInterfaceFactory $shopifyOrderFactory
+     * @param Ordermap $resourceModel
      * @param RequestHandler $requestHandler
      */
     public function __construct(
@@ -41,10 +40,9 @@ class OrderSynchronizer implements OrderSynchroniseInterface
         private readonly OrderRepositoryInterface $orderRepository,
         private readonly ResourceConnection $resourceConnection,
         private readonly LoggerInterface $logger,
+        private readonly SerializerInterface $serializer,
         private readonly OrdermapInterfaceFactory $shopifyOrderFactory,
         private readonly Ordermap $resourceModel,
-        private readonly SerializerInterface $serializer,
-        private readonly CollectionFactory $collectionFactory,
         private readonly RequestHandler $requestHandler
     ) {
     }
@@ -68,22 +66,16 @@ class OrderSynchronizer implements OrderSynchroniseInterface
             );
         }
 
-        /** @var Collection $collection */
-        $collection = $this->collectionFactory->create();
-        $collection->addFieldToFilter(
-            'mage_order_id',
-            $order->getEntityId()
-        )->addFieldToFilter(
-            'rule_id',
-            $configurableApi->getShopifyAccount()->getId()
-        );
+        /** @var OrdermapInterface $shopifyOrder */
+        $shopifyOrder = $this->shopifyOrderFactory->create();
+        $this->resourceModel->load($shopifyOrder, $order->getEntityId(), 'mage_order_id');
 
-        if ($collection->getSize()) {
+        if ($shopifyOrder->getId()) {
             throw new AlreadyExistsException(
                 __(
-                    'Order already synchronized order_id %1, seller ID %2',
+                    'Order already synchronized order_id %1, shopify ID %2',
                     $order->getEntityId(),
-                    $configurableApi->getShopifyAccount()->getSellerId()
+                    $shopifyOrder->getShopifyOrderId()
                 )
             );
         }
@@ -109,27 +101,24 @@ class OrderSynchronizer implements OrderSynchroniseInterface
             ]
         );
 
-        $shopifyOrder = $this->shopifyOrderFactory->create();
         $shopifyOrder->setMageOrderId($order->getEntityId())
             ->setStatus(1)
             ->setShopifyOrderId($decodedResponse['draft_order']['id'])
             ->setCreatedAt(time())
-            ->setRuleId($configurableApi->getShopifyAccount()->getId());
-        $this->resourceModel->save($shopifyOrder);
+            ->setData(
+                'rule_id',
+                $configurableApi->getShopifyAccount()->getId()
+            );
 
         $order->addCommentToStatusHistory(
-            sprintf(
-                'Successfully created draft order, Seller %s, Shopify Order ID %s',
-                $configurableApi->getShopifyAccount()->getSellerId(),
-                $decodedResponse['draft_order']['id']
-            ),
+            sprintf('Successfully created draft order, shopify ID %s', $decodedResponse['draft_order']['id']),
         );
         $this->orderRepository->save($order);
+        $this->resourceModel->save($shopifyOrder);
         $this->logger->info(
             '[ShopifyOrderSync] Successfully synced magento order',
             [
                 'shopifyAccount' => $configurableApi->getShopifyAccount()->getShopifyUserId(),
-                'sellerId' => $configurableApi->getShopifyAccount()->getSellerId(),
                 'orderIncrement' => $order->getIncrementId(),
                 'shopifyOrder' => $decodedResponse['draft_order']['id']
             ]
diff --git a/app/code/Comave/ShopifyAccounts/Observer/AddShopifyAccount.php b/app/code/Comave/ShopifyAccounts/Observer/AddShopifyAccount.php
index 001831a28..5d596a09c 100644
--- a/app/code/Comave/ShopifyAccounts/Observer/AddShopifyAccount.php
+++ b/app/code/Comave/ShopifyAccounts/Observer/AddShopifyAccount.php
@@ -44,7 +44,7 @@ class AddShopifyAccount implements ObserverInterface
         if (
             !$integration instanceof IntegrationInterface ||
             empty($integration->getSellerId()) ||
-            !str_contains($integration->getIntegrationType(), 'shopify')
+            $integration->getIntegrationType() !== 'shopify'
         ) {
             return;
         }
diff --git a/app/code/Comave/ShopifyAccounts/etc/di.xml b/app/code/Comave/ShopifyAccounts/etc/di.xml
index c7e70b072..fccc2684a 100644
--- a/app/code/Comave/ShopifyAccounts/etc/di.xml
+++ b/app/code/Comave/ShopifyAccounts/etc/di.xml
@@ -82,7 +82,7 @@
         </arguments>
     </virtualType>
 
-    <virtualType name="ShopifyMarketplaceIntegration" type="Comave\SellerApi\Model\BaseIntegration" shared="false">
+    <virtualType name="ShopifyMarketplaceIntegration" type="Comave\SellerApi\Model\BaseIntegration">
         <arguments>
             <argument xsi:type="string" name="integrationType">shopify</argument>
             <argument xsi:type="string" name="mainProductLinkTable">wk_mpmultishopifysynchronize_product</argument>
