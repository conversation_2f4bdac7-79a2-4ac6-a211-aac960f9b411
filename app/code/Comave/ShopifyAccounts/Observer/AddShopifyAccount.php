<?php

declare(strict_types=1);

namespace Comave\ShopifyAccounts\Observer;

use Comave\SellerApi\Api\IntegrationInterface;
use Comave\SellerApi\Api\IntegrationExtensionInterfaceFactory;
use Comave\ShopifyAccounts\Model\ConfigurableApi;
use Comave\ShopifyAccounts\Model\ConfigurableApiFactory;
use Comave\ShopifyAccounts\Model\OrderSynchronizerFactory;
use Comave\ShopifyAccounts\Model\ShopifyIntegrationRegistry;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Api\Data\ShopifyaccountsInterface;
use Webkul\MpMultiShopifyStoreMageConnect\Model\ResourceModel\Shopifyaccounts\CollectionFactory;

class AddShopifyAccount implements ObserverInterface
{
    /**
     * @param CollectionFactory $collectionFactory
     * @param ShopifyIntegrationRegistry $shopifyIntegrationRegistry
     * @param OrderSynchronizerFactory $synchronizerFactory
     * @param ConfigurableApiFactory $configurableApiFactory
     * @param IntegrationExtensionInterfaceFactory $extensionFactory
     */
    public function __construct(
        private readonly CollectionFactory $collectionFactory,
        private readonly ShopifyIntegrationRegistry $shopifyIntegrationRegistry,
        private readonly OrderSynchronizerFactory $synchronizerFactory,
        private readonly ConfigurableApiFactory $configurableApiFactory,
        private readonly IntegrationExtensionInterfaceFactory $extensionFactory
    ) {
    }

    /**
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        $integration = $observer->getData('integration');

        if (
            !$integration instanceof IntegrationInterface ||
            empty($integration->getSellerId()) ||
            !str_contains($integration->getIntegrationType(), 'shopify')
        ) {
            return;
        }

        $extensions = $integration->getExtensionAttributes();

        if (null === $extensions) {
            $extensions = $this->extensionFactory->create();
        }

        $collection = $this->collectionFactory->create();
        $collection->addFieldToFilter(
            'seller_id',
            $integration->getSellerId()
        );

        if (!$collection->getSize()) {
            return;
        }

        /** @var ShopifyaccountsInterface $shopifyAccount */
        $shopifyAccount = $collection->getFirstItem();
        $extensions->setShopifyAccount($shopifyAccount);
        $integration->setIntegrationType(
            sprintf(
                '%s_%s',
                $integration->getIntegrationType(),
                $shopifyAccount->getId()
            )
        );

        $configurableApi = $this->configurableApiFactory->create([
            'shopifyAccountId' => $shopifyAccount->getId()
        ])->setEndpoint(ConfigurableApi::SHOPIFY_ORDER_ENDPOINT_TYPE);
        $orderSynchronizer = $this->synchronizerFactory->create();
        $this->shopifyIntegrationRegistry->addSynchronizer(
            $integration->getIntegrationType(),
            $orderSynchronizer
        )->addApiConfiguration(
            $integration->getIntegrationType(),
            $configurableApi
        );
        $integration->setExtensionAttributes($extensions);
    }
}
