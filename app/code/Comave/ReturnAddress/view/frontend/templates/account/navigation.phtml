<?php
/** @var \Magento\Framework\View\Element\Template $block */
/** @var \Magento\Framework\Escaper $escaper */
/** @var \Webkul\Marketplace\Helper\Data $_helper */
$_helper = $this->helper(\Webkul\Marketplace\Helper\Data::class);
$isPartner = $_helper->isSeller();
$isSellerGroup = $_helper->isSellerGroupModuleInstalled();
?>
<?php if ($isPartner): ?>
    <?php if (($isSellerGroup && $_helper->isAllowedAction('returnaddress/index/index')) || !$isSellerGroup): ?>
        <li data-ui-id="menu-webkul-marketplace-setting-return-address" class="item-menu parent level-1">
            <strong class="wk-mp-submenu-group-title">
                <span><?= $escaper->escapeHtml(__('Returns')) ?></span>
            </strong>
            <div class="wk-mp-submenu">
                <ul>
                    <li class="level-2">
                        <a href="<?= $escaper->escapeUrl($block->getUrl('returnaddress', ['_secure' => $block->getRequest()->isSecure()])); ?>">
                            <span><?= $escaper->escapeHtml(__('Return Address')) ?></span>
                        </a>
                    </li>
                </ul>
            </div>
        </li>
    <?php endif; ?>
<?php endif; ?>

