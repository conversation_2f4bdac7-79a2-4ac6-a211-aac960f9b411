<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\RmaGraphQl\Model\Resolver\CustomerOrder\Item\IsEligible">
        <plugin name="checkForGracePeriod" type="Comave\RmaGraphQl\Plugin\CheckIsEligible"/>
    </type>

    <type name="Comave\RmaGraphQl\Plugin\CheckIsEligible">
        <arguments>
            <argument name="logger" xsi:type="object">ComaveSellerReturns</argument>
        </arguments>
    </type>

    <type name="Comave\RmaGraphQl\Plugin\CheckIsListEligible">
        <arguments>
            <argument name="logger" xsi:type="object">ComaveSellerReturns</argument>
        </arguments>
    </type>

    <type name="Magento\RmaGraphQl\Model\Resolver\CustomerOrder\EligibleItems">
        <plugin name="checkListForGracePeriod" type="Comave\RmaGraphQl\Plugin\CheckIsListEligible"/>
    </type>

    <type name="Comave\RmaGraphQl\Service\ShippingFormatter">
        <arguments>
            <argument name="logger" xsi:type="object">ComaveSellerReturns</argument>
        </arguments>
    </type>

    <type name="Magento\RmaGraphQl\Model\Formatter\Rma">
        <arguments>
            <argument xsi:type="object" name="shippingFormatter">Comave\RmaGraphQl\Service\ShippingFormatter</argument>
        </arguments>
        <plugin name="retrieveComaveStatus" type="Comave\RmaGraphQl\Plugin\SetComaveStatus"/>
    </type>

    <type name="Magento\RmaGraphQl\Model\Formatter\RmaItem">
        <arguments>
            <argument xsi:type="object" name="customAttributeFormatter">Comave\RmaGraphQl\Service\OptionProvider</argument>
        </arguments>
    </type>

    <type name="Comave\RmaGraphQl\Service\OptionProvider">
        <arguments>
            <argument xsi:type="array" name="customOptionAttributes">
                <item name="reason" xsi:type="object">Comave\Rma\Model\Source\Reasons</item>
                <item name="resolution" xsi:type="object">Comave\Rma\Model\Source\Resolution</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Framework\GraphQl\Schema\Type\Enum\DefaultDataMapper">
        <arguments>
            <argument name="map" xsi:type="array">
                <item name="ReturnStatus" xsi:type="array">
                    <item xsi:type="string" name="return_initiated">return_initiated</item>
                    <item xsi:type="string" name="na">na</item>
                    <item xsi:type="string" name="return_completed">return_completed</item>
                    <item xsi:type="string" name="partial_refund">partial_refund</item>
                    <item xsi:type="string" name="refunded">refunded</item>
                    <item xsi:type="string" name="return_declined">return_declined</item>
                    <item xsi:type="string" name="processed_and_closed">processed_closed</item>
                </item>
            </argument>
        </arguments>
    </type>

    <type name="Comave\RmaGraphQl\Model\Resolver\RequestReturn">
        <arguments>
            <argument name="logger" xsi:type="object">ComaveSellerReturns</argument>
        </arguments>
    </type>

    <type name="Comave\RmaGraphQl\Model\Resolver\SellerDetails">
        <arguments>
            <argument name="logger" xsi:type="object">ComaveSellerReturns</argument>
        </arguments>
    </type>

    <type name="Magento\RmaGraphQl\Model\Rma\Item\Builder">
        <arguments>
            <argument xsi:type="object" name="optionValueProvider">Comave\RmaGraphQl\Service\CustomOptionProvider</argument>
        </arguments>
    </type>

    <type name="Comave\RmaGraphQl\Service\CustomOptionProvider">
        <arguments>
            <argument xsi:type="array" name="newOptionsProviders">
                <item name="reason" xsi:type="object">Comave\Rma\Model\Source\Reasons</item>
                <item name="resolution" xsi:type="object">Comave\Rma\Model\Source\Resolution</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Rma\Model\ResourceModel\Item">
        <plugin name="ensureReasonSave" type="Comave\RmaGraphQl\Plugin\ItemAttributeSave"/>
    </type>
</config>
