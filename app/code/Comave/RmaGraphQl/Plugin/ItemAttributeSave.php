<?php

declare(strict_types=1);

namespace Comave\RmaGraphQl\Plugin;

use Magento\Framework\Model\AbstractModel;
use Magento\Rma\Model\ResourceModel\Item;

class ItemAttributeSave
{
    /**
     * Seems there's a bug with the core that doesn't handle attributes properly for multiple items in a single request
     * @param Item $itemResource
     * @param callable $proceed
     * @param AbstractModel $rmaItem
     * @return Item
     * @throws \Exception
     */
    public function aroundSave(
        Item $itemResource,
        callable $proceed,
        AbstractModel $rmaItem
    ): Item {
        $reasonValue = $rmaItem->getReason();
        $condition = $rmaItem->getCondition();
        $resolution = $rmaItem->getResolution();

        $result = $proceed($rmaItem);

        if (!empty($condition)) {
            $rmaItem->setCondition($condition);
            $itemResource->saveAttribute(
                $rmaItem,
                'condition'
            );
        }

        if (!empty($reasonValue)) {
            $rmaItem->setReason($reasonValue);
            $itemResource->saveAttribute(
                $rmaItem,
                'reason'
            );
        }

        if (!empty($resolution)) {
            $rmaItem->setResolution($resolution);
            $itemResource->saveAttribute(
                $rmaItem,
                'resolution'
            );
        }

        return $result;
    }
}
