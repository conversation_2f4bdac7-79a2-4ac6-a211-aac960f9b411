<?php

declare(strict_types=1);

namespace Comave\RmaGraphQl\Service;

use Magento\Eav\Model\ResourceModel\Entity\Attribute\OptionValueProvider;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Framework\Phrase;

class CustomOptionProvider extends OptionValueProvider
{
    /**
     * @param ResourceConnection $connection
     * @param OptionSourceInterface[] $newOptionsProviders
     */
    public function __construct(
        ResourceConnection $connection,
        private readonly array $newOptionsProviders
    ) {
        parent::__construct($connection);
    }

    /**
     * @param int $optionId
     * @return string|null
     */
    public function get(int $optionId): ?string
    {
        /** @var OptionSourceInterface $provider */
        foreach ($this->newOptionsProviders as $provider) {
            $options = $provider->toOptionArray();

            foreach ($options as $option) {
                if ((int) $option['value'] !== $optionId) {
                    continue;
                }

                return $option['label'] instanceof Phrase ?
                    $option['label']->render() :
                    $option['label'];
            }
        }

        return parent::get($optionId);
    }
}
